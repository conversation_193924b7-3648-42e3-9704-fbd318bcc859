# SpigaUI Design Plan

**Project**: Modern Web Interface for SpigaMonde Crawler  
**Date**: 2025-08-28  
**Status**: Design Phase  

---

## 🎯 **Design Goals**

### **Primary Objectives**
1. **Scalable Architecture**: Replace tabbed interface with multi-page router system
2. **Maintainable Codebase**: Break down monolithic JavaScript into focused components
3. **Real-time Experience**: Provide live progress tracking and updates
4. **Responsive Design**: Work seamlessly across desktop, tablet, and mobile
5. **Developer Experience**: Clean separation of concerns and type safety

### **Key Problems Solved**
- ❌ **Tabbed Layout Limitations** → ✅ **Multi-page Router with Unlimited Space**
- ❌ **1,928-line Monolithic JS** → ✅ **Component-based Architecture (50-200 lines each)**
- ❌ **Fake Progress Simulation** → ✅ **Real-time SSE Progress Tracking**
- ❌ **Tight Coupling** → ✅ **Clean Service Layer Separation**
- ❌ **Poor Mobile Experience** → ✅ **Responsive Design Patterns**

---

## 🏗️ **Architecture Overview**

### **Technology Stack**
```
Frontend:  React 18 + TypeScript + Vite + Tailwind CSS
State:     Redux Toolkit + React Query
Routing:   React Router v6
Backend:   FastAPI + Celery + Redis
Package:   UV (Python) + NPM (Frontend)
Real-time: Server-Sent Events (SSE)
```

### **System Architecture**
```
┌─────────────────┐    HTTP/SSE     ┌──────────────────┐    Celery     ┌─────────────────┐
│   React SPA     │ ◄──────────────► │   FastAPI        │ ◄────────────► │   Workers       │
│                 │                 │                  │               │                 │
│ • Multi-page    │                 │ • Async APIs     │               │ • SpigaMonde    │
│ • Components    │                 │ • SSE Streams    │               │ • Real Progress │
│ • Real-time UI  │                 │ • Job Management │               │ • Background    │
└─────────────────┘                 └──────────────────┘               └─────────────────┘
         │                                    │                                  │
         │                                    │                                  │
         ▼                                    ▼                                  ▼
┌─────────────────┐                 ┌──────────────────┐               ┌─────────────────┐
│   Redux Store   │                 │   PostgreSQL     │               │   Redis         │
│                 │                 │                  │               │                 │
│ • Job State     │                 │ • Job Records    │               │ • Task Queue    │
│ • UI State      │                 │ • Results Data   │               │ • Real-time     │
│ • Settings      │                 │ • User Config    │               │ • Pub/Sub       │
└─────────────────┘                 └──────────────────┘               └─────────────────┘
```

---

## 🎨 **User Interface Design**

### **Navigation Structure**
```
SpigaUI/
├── 📊 Dashboard              # Overview, recent activity, system status
├── 🕷️ Start Crawl           # Crawl configuration and launch
├── 📋 Jobs                   # Job list, monitoring, management
│   ├── Active Jobs          # Currently running jobs
│   ├── Job History          # Completed/failed jobs
│   └── Job Details /:id     # Individual job progress & results
├── 📈 Analytics              # Charts, statistics, performance trends
├── 📁 Results                # Browse, search, and export crawl data
├── ⚙️ Settings               # Configuration, preferences, API keys
└── 📚 Help                   # Documentation, tutorials, support
```

### **Layout Pattern**
```
┌─────────────────────────────────────────────────────────────────┐
│ Header: Logo | Breadcrumbs | Search | User Menu                 │
├─────────────┬───────────────────────────────────────────────────┤
│             │                                                   │
│  Sidebar    │                Main Content Area                  │
│             │                                                   │
│ • Dashboard │  ┌─────────────────────────────────────────────┐  │
│ • Crawl     │  │                                             │  │
│ • Jobs      │  │         Page-specific Content               │  │
│ • Analytics │  │                                             │  │
│ • Results   │  │  • Forms, Tables, Charts                   │  │
│ • Settings  │  │  • Real-time Updates                       │  │
│             │  │  • Interactive Components                  │  │
│             │  │                                             │  │
│             │  └─────────────────────────────────────────────┘  │
│             │                                                   │
├─────────────┴───────────────────────────────────────────────────┤
│ Footer: Status | Version | Links                                │
└─────────────────────────────────────────────────────────────────┘
```

### **Responsive Breakpoints**
- **Desktop (1024px+)**: Full sidebar + main content
- **Tablet (768-1023px)**: Collapsible sidebar
- **Mobile (< 768px)**: Hidden sidebar with hamburger menu

---

## 📱 **Page Designs**

### **1. Dashboard Page**
**Purpose**: System overview and quick access to common actions
**Components**:
- System status cards (jobs running, queue size, system health)
- Recent activity timeline
- Quick action buttons (Start Crawl, View Jobs)
- Performance charts (pages/hour, success rate)
- Resource usage indicators

### **2. Start Crawl Page**
**Purpose**: Configure and launch new crawl jobs
**Components**:
- URL input with validation
- Crawl configuration form (depth, limits, options)
- Advanced settings (headers, cookies, user agent)
- Template selection (save/load configurations)
- Preview and validation before launch

### **3. Jobs Page**
**Purpose**: Monitor and manage crawl jobs
**Components**:
- Jobs table with filtering and sorting
- Real-time status updates
- Bulk actions (cancel, restart, delete)
- Job search and filtering
- Pagination for large job lists

### **4. Job Details Page**
**Purpose**: Detailed view of individual job progress and results
**Components**:
- Real-time progress bar and statistics
- Live log streaming
- Results preview and download
- Job configuration display
- Action buttons (cancel, restart, export)

### **5. Analytics Page**
**Purpose**: Performance insights and trends
**Components**:
- Interactive charts (success rates, performance trends)
- Time range selectors
- Comparative analysis tools
- Export capabilities for reports
- Custom dashboard creation

### **6. Results Page**
**Purpose**: Browse, search, and export crawled data
**Components**:
- Results browser with search and filters
- Data export tools (JSON, CSV, XML)
- Bulk operations on results
- Data visualization options
- Integration with external tools

---

## 🎨 **Design System**

### **Color Palette**
```css
Primary:   #3B82F6 (Blue)     /* Actions, links, primary buttons */
Secondary: #6B7280 (Gray)     /* Secondary text, borders */
Success:   #10B981 (Green)    /* Success states, completed jobs */
Warning:   #F59E0B (Amber)    /* Warnings, pending states */
Error:     #EF4444 (Red)      /* Errors, failed jobs */
Info:      #06B6D4 (Cyan)     /* Information, running jobs */
```

### **Typography**
```css
Headings:  Inter, system-ui, sans-serif
Body:      Inter, system-ui, sans-serif
Code:      'JetBrains Mono', 'Fira Code', monospace
```

### **Component Library**
- **Buttons**: Primary, Secondary, Outline, Ghost variants
- **Forms**: Input, Select, Checkbox, Radio, Switch components
- **Data Display**: Table, Card, Badge, Progress, Chart components
- **Feedback**: Alert, Toast, Modal, Loading components
- **Navigation**: Breadcrumb, Pagination, Tab components

---

## 🔄 **Real-time Features**

### **Server-Sent Events (SSE)**
- **Job Progress**: Live updates on crawl progress
- **System Status**: Real-time system health monitoring
- **Notifications**: Instant alerts for job completion/failures
- **Live Logs**: Streaming log output during job execution

### **State Synchronization**
- **Optimistic Updates**: Immediate UI feedback for user actions
- **Conflict Resolution**: Handle concurrent updates gracefully
- **Offline Support**: Queue actions when connection is lost
- **Auto-reconnection**: Seamless reconnection after network issues

---

## 📊 **Performance Considerations**

### **Frontend Optimization**
- **Code Splitting**: Lazy load pages and components
- **Bundle Optimization**: Tree shaking and minification
- **Caching Strategy**: Aggressive caching for static assets
- **Virtual Scrolling**: Handle large datasets efficiently

### **Backend Optimization**
- **Async Processing**: Non-blocking request handling
- **Connection Pooling**: Efficient database connections
- **Caching Layer**: Redis for frequently accessed data
- **Rate Limiting**: Protect against abuse and overload

---

## 🔒 **Security & Accessibility**

### **Security Features**
- **CORS Configuration**: Proper cross-origin resource sharing
- **Input Validation**: Comprehensive input sanitization
- **Rate Limiting**: API endpoint protection
- **Error Handling**: Secure error messages without information leakage

### **Accessibility (WCAG 2.1 AA)**
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and roles
- **Color Contrast**: Minimum 4.5:1 contrast ratio
- **Focus Management**: Clear focus indicators and logical tab order

---

## 🧪 **Testing Strategy**

### **Frontend Testing**
- **Unit Tests**: Component testing with React Testing Library
- **Integration Tests**: API integration and user flows
- **E2E Tests**: Critical user journeys with Playwright
- **Visual Regression**: Screenshot comparison testing

### **Backend Testing**
- **Unit Tests**: Service and utility function testing
- **API Tests**: Endpoint testing with FastAPI TestClient
- **Integration Tests**: Database and external service integration
- **Load Tests**: Performance testing under load

This design plan provides the foundation for a scalable, maintainable, and user-friendly web interface that solves all the architectural problems identified in the legacy system.
