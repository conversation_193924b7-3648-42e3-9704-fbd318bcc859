"""
Health check endpoints for SpigaUI.

Provides system health and status information.
"""

from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
import structlog

from spigaui.core.config import get_settings

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Health check endpoint.
    
    Returns the current health status of the application and its dependencies.
    """
    logger.debug("Health check requested")
    
    settings = get_settings()
    
    # Basic health check
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "0.1.0",
        "services": {
            "api": "healthy",
            "redis": "unknown",  # TODO: Add Redis health check
            "spigamonde": "unknown",  # TODO: Add SpigaMonde health check
        },
        "config": {
            "debug": settings.debug,
            "log_level": settings.log_level,
        }
    }
    
    logger.info("Health check completed", status=health_status["status"])
    return health_status


@router.get("/health/ready")
async def readiness_check() -> Dict[str, Any]:
    """
    Readiness check endpoint.
    
    Returns whether the application is ready to serve requests.
    """
    logger.debug("Readiness check requested")
    
    # TODO: Add actual readiness checks
    # - Redis connection
    # - SpigaMonde availability
    # - Database connectivity
    
    ready_status = {
        "ready": True,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": {
            "redis": True,  # TODO: Implement actual check
            "spigamonde": True,  # TODO: Implement actual check
        }
    }
    
    if not ready_status["ready"]:
        logger.warning("Readiness check failed", checks=ready_status["checks"])
        raise HTTPException(status_code=503, detail="Service not ready")
    
    logger.info("Readiness check passed")
    return ready_status


@router.get("/health/live")
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check endpoint.
    
    Returns whether the application is alive and responding.
    """
    logger.debug("Liveness check requested")
    
    return {
        "alive": True,
        "timestamp": datetime.utcnow().isoformat(),
    }
