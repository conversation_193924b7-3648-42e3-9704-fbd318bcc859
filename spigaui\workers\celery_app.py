"""
Celery application configuration for SpigaUI.

Configures Celery for background task processing.
"""

from celery import Celery
import structlog

from spigaui.core.config import get_settings

# Setup logging
logger = structlog.get_logger(__name__)

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "spigaui",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=["spigaui.workers.crawl_tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Task routing (optional)
celery_app.conf.task_routes = {
    "spigaui.workers.crawl_tasks.*": {"queue": "crawl"},
}

logger.info("Celery app configured", broker=settings.celery_broker_url)
