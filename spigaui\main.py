"""
SpigaUI FastAPI Application

Main application entry point for the SpigaUI web interface.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

from spigaui.api import health, crawl, jobs
from spigaui.core.config import get_settings
from spigaui.core.logging import setup_logging, configure_uvicorn_logging
from spigaui.core.middleware import (
    LoggingMiddleware,
    ErrorHandlingMiddleware,
    SecurityHeadersMiddleware,
)

# Setup structured logging
setup_logging()
configure_uvicorn_logging()
logger = structlog.get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    settings = get_settings()
    logger.info("Starting SpigaUI application", version="0.1.0")
    
    # Startup logic
    try:
        # Initialize any required services here
        logger.info("Application startup complete")
        yield
    finally:
        # Cleanup logic
        logger.info("Shutting down SpigaUI application")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()

    # Configure docs URLs based on settings
    docs_url = "/api/docs" if settings.enable_docs else None
    redoc_url = "/api/redoc" if settings.enable_docs else None
    openapi_url = "/api/openapi.json" if settings.enable_docs else None

    app = FastAPI(
        title="SpigaUI",
        description="Modern web interface for SpigaMonde crawler",
        version="0.1.0",
        docs_url=docs_url,
        redoc_url=redoc_url,
        openapi_url=openapi_url,
        lifespan=lifespan,
    )

    # Add custom middleware (order matters - first added is outermost)
    app.add_middleware(SecurityHeadersMiddleware)
    app.add_middleware(ErrorHandlingMiddleware)
    app.add_middleware(LoggingMiddleware)

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routers
    app.include_router(health.router, prefix="/api", tags=["health"])
    app.include_router(crawl.router, prefix="/api", tags=["crawl"])
    app.include_router(jobs.router, prefix="/api", tags=["jobs"])
    
    # Serve static files (frontend build)
    if settings.serve_static:
        app.mount("/", StaticFiles(directory="frontend/dist", html=True), name="static")
    
    return app


# Create the application instance
app = create_app()


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "SpigaUI - Modern Web Interface for SpigaMonde",
        "version": "0.1.0",
        "docs": "/api/docs",
    }


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "spigaui.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,  # Enable auto-reload in debug mode
        log_level=settings.log_level.lower(),
        # Smart file watching - only watch source files, exclude logs
        reload_dirs=["spigaui"] if settings.debug else None,  # Only watch source code directory
        reload_excludes=[
            "*.log", "*.pyc", "*.pyo", "*.pyd",
            "__pycache__", "logs/*", "*.tmp", "*.temp",
            ".git/*", "node_modules/*", "*.sqlite", "*.db"
        ] if settings.debug else None,
    )
