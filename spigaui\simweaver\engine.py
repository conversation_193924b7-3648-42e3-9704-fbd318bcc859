"""
SimWeaver main engine.

The core crawler simulation engine that manages jobs and orchestrates crawling.
"""

import asyncio
import random
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Set, Any

import structlog

from spigaui.core.logging import get_logger, log_job_event
from .models import CrawlStatus, PageStatus, CrawlPage, CrawlStats, CrawlJob
from .config import SimWeaverConfig
from .data_generator import DataGenerator
from .logging_config import SimWeaverLoggingConfig, SimWeaverLogger


class SimWeaver:
    """Simulated web crawler for UI development."""
    
    def __init__(self, logging_config: Optional[SimWeaverLoggingConfig] = None):
        self.jobs: Dict[str, CrawlJob] = {}
        self.active_jobs: Set[str] = set()
        self.logger = get_logger("simweaver")

        # Enhanced logging configuration
        self.logging_config = logging_config or SimWeaverLoggingConfig()
        self.enhanced_logger = SimWeaverLogger(self.logging_config)

        # Default configuration
        self.default_config = SimWeaverConfig()

        # Performance tracking
        self.operation_timers: Dict[str, float] = {}

        self.enhanced_logger.state(
            "SimWeaver engine initialized",
            logging_categories_enabled=self._get_enabled_categories(),
            default_config=self.default_config.to_dict()
        )

    def _get_enabled_categories(self) -> List[str]:
        """Get list of enabled logging categories."""
        from .logging_config import LogCategory
        return [
            cat.value for cat in LogCategory
            if self.logging_config.is_enabled(cat)
        ]

    def _start_operation_timer(self, operation: str, **context) -> str:
        """Start timing an operation."""
        timer_id = f"{operation}_{uuid.uuid4().hex[:8]}"
        self.operation_timers[timer_id] = time.time()
        self.enhanced_logger.log_operation_start(operation, timer_id=timer_id, **context)
        return timer_id

    def _end_operation_timer(self, timer_id: str, operation: str, **context):
        """End timing an operation and log results."""
        if timer_id in self.operation_timers:
            duration = time.time() - self.operation_timers[timer_id]
            del self.operation_timers[timer_id]
            self.enhanced_logger.log_operation_end(operation, duration, timer_id=timer_id, **context)
            return duration
        return None
    
    def create_job(
        self,
        name: str,
        start_url: str,
        max_pages: int = 100,
        max_depth: int = 3,
        delay_min: float = 0.1,
        delay_max: float = 2.0,
        failure_rate: float = 0.05,
        config: Optional[SimWeaverConfig] = None
    ) -> str:
        """Create a new crawl job with comprehensive logging."""
        timer_id = self._start_operation_timer("create_job", name=name, start_url=start_url)

        try:
            job_id = str(uuid.uuid4())

            # Use provided config or create from parameters
            if config is None:
                config = SimWeaverConfig(
                    max_pages=max_pages,
                    max_depth=max_depth,
                    delay_min=delay_min,
                    delay_max=delay_max,
                    failure_rate=failure_rate
                )
                self.enhanced_logger.debug(
                    "Created default config for job",
                    job_id=job_id,
                    config=config.to_dict()
                )
            else:
                self.enhanced_logger.debug(
                    "Using provided config for job",
                    job_id=job_id,
                    config=config.to_dict()
                )

            # Validate configuration
            self._validate_job_config(config, job_id)

            job = CrawlJob(
                job_id=job_id,
                name=name,
                start_url=start_url,
                max_pages=config.max_pages,
                max_depth=config.max_depth,
                delay_min=config.delay_min,
                delay_max=config.delay_max,
                failure_rate=config.failure_rate
            )

            # Add the starting page
            start_page = CrawlPage(
                url=start_url,
                depth=0
            )
            job.pages[start_url] = start_page
            job.stats.total_pages = 1
            job.stats.pages_pending = 1

            # Log page discovery
            self.enhanced_logger.log_page_discovered(start_url, 0)

            self.jobs[job_id] = job

            # Log state transition
            self.enhanced_logger.log_state_transition(
                "job", job_id, "none", CrawlStatus.QUEUED.value
            )

            # Log job creation event
            log_job_event(
                job_id=job_id,
                event_type="job_created",
                name=name,
                start_url=start_url,
                max_pages=config.max_pages
            )

            self.enhanced_logger.debug(
                "Job created successfully",
                job_id=job_id,
                name=name,
                start_url=start_url,
                config_summary={
                    "max_pages": config.max_pages,
                    "max_depth": config.max_depth,
                    "failure_rate": config.failure_rate
                },
                total_jobs=len(self.jobs)
            )

            # Simulate security event occasionally
            if (self.logging_config.simulate_security_events and
                random.random() < self.logging_config.security_event_rate):
                self.enhanced_logger.log_security_event(
                    "job_creation_monitored",
                    job_id=job_id,
                    user_agent="SimWeaver/1.0",
                    source_ip="127.0.0.1"
                )

            self._end_operation_timer(timer_id, "create_job", job_id=job_id, success=True)
            return job_id

        except Exception as e:
            self.enhanced_logger.error(
                "Failed to create job",
                job_name=name,
                start_url=start_url,
                error=str(e),
                error_type=type(e).__name__
            )
            self._end_operation_timer(timer_id, "create_job", success=False, error=str(e))
            raise

    def _validate_job_config(self, config: SimWeaverConfig, job_id: str):
        """Validate job configuration and log any issues."""
        issues = []

        if config.max_pages > 10000:
            issues.append("max_pages exceeds recommended limit of 10000")

        if config.failure_rate > 0.5:
            issues.append("failure_rate is very high (>50%)")

        if config.delay_max < config.delay_min:
            issues.append("delay_max is less than delay_min")

        if issues:
            self.enhanced_logger.debug(
                "Job configuration validation warnings",
                job_id=job_id,
                issues=issues,
                config=config.to_dict()
            )
    
    def get_job(self, job_id: str) -> Optional[CrawlJob]:
        """Get a crawl job by ID."""
        return self.jobs.get(job_id)
    
    def list_jobs(self) -> List[CrawlJob]:
        """List all crawl jobs."""
        return list(self.jobs.values())
    
    def get_job_stats(self, job_id: str) -> Optional[CrawlStats]:
        """Get statistics for a specific job."""
        job = self.get_job(job_id)
        return job.stats if job else None
    
    def delete_job(self, job_id: str) -> bool:
        """Delete a crawl job."""
        if job_id in self.jobs:
            job = self.jobs[job_id]
            
            # Can't delete active jobs
            if job.is_active():
                return False
            
            del self.jobs[job_id]
            self.active_jobs.discard(job_id)
            
            log_job_event(
                job_id=job_id,
                event_type="job_deleted"
            )
            
            return True
        
        return False
    
    async def start_job(self, job_id: str) -> bool:
        """Start a crawl job with comprehensive logging."""
        timer_id = self._start_operation_timer("start_job", job_id=job_id)

        try:
            job = self.get_job(job_id)
            if not job:
                self.enhanced_logger.error(
                    "Cannot start job: job not found",
                    job_id=job_id,
                    total_jobs=len(self.jobs)
                )
                self._end_operation_timer(timer_id, "start_job", success=False, reason="job_not_found")
                return False

            if not job.can_be_started():
                self.enhanced_logger.debug(
                    "Cannot start job: invalid state",
                    job_id=job_id,
                    current_state=job.status.value,
                    required_state=CrawlStatus.QUEUED.value
                )
                self._end_operation_timer(timer_id, "start_job", success=False, reason="invalid_state")
                return False

            # Log state transition
            self.enhanced_logger.log_state_transition(
                "job", job_id, job.status.value, CrawlStatus.STARTING.value
            )

            job.status = CrawlStatus.STARTING
            job.started_at = datetime.now()
            job.stats.start_time = job.started_at
            self.active_jobs.add(job_id)

            # Log job startup details
            self.enhanced_logger.debug(
                "Job startup initiated",
                job_id=job_id,
                name=job.name,
                start_url=job.start_url,
                config_summary={
                    "max_pages": job.max_pages,
                    "max_depth": job.max_depth,
                    "failure_rate": job.failure_rate
                },
                active_jobs_count=len(self.active_jobs)
            )

            log_job_event(
                job_id=job_id,
                event_type="job_started",
                start_url=job.start_url
            )

            # Performance metrics
            self.enhanced_logger.performance(
                "Job startup performance",
                job_id=job_id,
                startup_time=datetime.now().isoformat(),
                memory_usage_estimate=len(self.jobs) * 1024  # Rough estimate
            )

            # Start the crawling simulation in the background
            asyncio.create_task(self._simulate_crawl(job))

            self._end_operation_timer(timer_id, "start_job", job_id=job_id, success=True)
            return True

        except Exception as e:
            self.enhanced_logger.error(
                "Failed to start job",
                job_id=job_id,
                error=str(e),
                error_type=type(e).__name__
            )
            self._end_operation_timer(timer_id, "start_job", success=False, error=str(e))
            raise
    
    async def stop_job(self, job_id: str) -> bool:
        """Stop a running crawl job."""
        job = self.get_job(job_id)
        if not job or not job.can_be_stopped():
            return False
        
        job._should_stop = True
        job.status = CrawlStatus.CANCELLED
        job.completed_at = datetime.now()
        self.active_jobs.discard(job_id)
        
        log_job_event(
            job_id=job_id,
            event_type="job_cancelled",
            pages_crawled=job.stats.pages_crawled
        )
        
        self.logger.info(
            "SimWeaver job stopped",
            job_id=job_id,
            name=job.name
        )
        
        return True
    
    async def pause_job(self, job_id: str) -> bool:
        """Pause a running crawl job."""
        job = self.get_job(job_id)
        if not job or not job.can_be_paused():
            return False
        
        job._is_paused = True
        job.status = CrawlStatus.PAUSED
        
        log_job_event(
            job_id=job_id,
            event_type="job_paused"
        )
        
        self.logger.info(
            "SimWeaver job paused",
            job_id=job_id,
            name=job.name
        )
        
        return True
    
    async def resume_job(self, job_id: str) -> bool:
        """Resume a paused crawl job."""
        job = self.get_job(job_id)
        if not job or not job.can_be_resumed():
            return False
        
        job._is_paused = False
        job.status = CrawlStatus.RUNNING
        
        log_job_event(
            job_id=job_id,
            event_type="job_resumed"
        )
        
        self.logger.info(
            "SimWeaver job resumed",
            job_id=job_id,
            name=job.name
        )
        
        return True
    
    def get_active_job_count(self) -> int:
        """Get the number of currently active jobs."""
        return len(self.active_jobs)
    
    def get_job_count_by_status(self, status: CrawlStatus) -> int:
        """Get the number of jobs with a specific status."""
        return sum(1 for job in self.jobs.values() if job.status == status)
    
    def cleanup_completed_jobs(self, max_completed: int = 100) -> int:
        """Clean up old completed jobs, keeping only the most recent ones."""
        completed_jobs = [
            job for job in self.jobs.values() 
            if job.status in [CrawlStatus.COMPLETED, CrawlStatus.FAILED, CrawlStatus.CANCELLED]
        ]
        
        if len(completed_jobs) <= max_completed:
            return 0
        
        # Sort by completion time, keep the most recent
        completed_jobs.sort(key=lambda j: j.completed_at or datetime.min, reverse=True)
        jobs_to_delete = completed_jobs[max_completed:]
        
        deleted_count = 0
        for job in jobs_to_delete:
            if self.delete_job(job.job_id):
                deleted_count += 1
        
        self.logger.info(
            "SimWeaver cleanup completed",
            deleted_count=deleted_count,
            remaining_completed=len(completed_jobs) - deleted_count
        )
        
        return deleted_count

    async def emergency_stop_all(self) -> Dict[str, Any]:
        """
        Emergency stop all running jobs immediately.

        This is a safety method to quickly stop all crawling activity.
        """
        self.enhanced_logger.security(
            "Emergency stop all jobs initiated",
            active_jobs_count=len(self.active_jobs),
            total_jobs_count=len(self.jobs)
        )

        stopped_jobs = []
        failed_jobs = []

        # Stop all active jobs
        for job_id in list(self.active_jobs):
            try:
                success = await self.stop_job(job_id)
                if success:
                    stopped_jobs.append(job_id)
                else:
                    failed_jobs.append(job_id)
            except Exception as e:
                self.enhanced_logger.error(
                    "Failed to stop job during emergency stop",
                    job_id=job_id,
                    error=str(e)
                )
                failed_jobs.append(job_id)

        # Force clear active jobs as safety measure
        self.active_jobs.clear()

        result = {
            "stopped_jobs": stopped_jobs,
            "failed_jobs": failed_jobs,
            "total_stopped": len(stopped_jobs),
            "total_failed": len(failed_jobs)
        }

        self.enhanced_logger.security(
            "Emergency stop completed",
            **result
        )

        return result

    def nuclear_reset(self) -> Dict[str, Any]:
        """
        Nuclear option - completely reset the SimWeaver engine.

        Clears all jobs, active jobs, and operation timers.
        Use only if emergency_stop_all doesn't work.
        """
        self.enhanced_logger.security(
            "Nuclear reset initiated - clearing all SimWeaver state",
            jobs_to_clear=len(self.jobs),
            active_jobs_to_clear=len(self.active_jobs),
            timers_to_clear=len(self.operation_timers)
        )

        # Clear all state
        jobs_cleared = len(self.jobs)
        active_cleared = len(self.active_jobs)
        timers_cleared = len(self.operation_timers)

        self.jobs.clear()
        self.active_jobs.clear()
        self.operation_timers.clear()

        result = {
            "jobs_cleared": jobs_cleared,
            "active_jobs_cleared": active_cleared,
            "timers_cleared": timers_cleared,
            "engine_reset": True
        }

        self.enhanced_logger.security(
            "Nuclear reset completed - SimWeaver engine reset",
            **result
        )

        return result

    def get_engine_health(self) -> Dict[str, Any]:
        """Get engine health status for monitoring."""
        total_jobs = len(self.jobs)
        active_jobs = len(self.active_jobs)
        active_timers = len(self.operation_timers)

        # Detect potential issues
        warnings = []
        if active_jobs > 10:
            warnings.append("high_active_jobs")
        if active_timers > 50:
            warnings.append("high_active_timers")
        if total_jobs > 1000:
            warnings.append("high_total_jobs")

        # Check for stuck jobs (running for more than 1 hour)
        stuck_jobs = []
        for job in self.jobs.values():
            if (job.status.value == "running" and
                job.started_at and
                (datetime.now() - job.started_at).total_seconds() > 3600):
                stuck_jobs.append(job.job_id)

        if stuck_jobs:
            warnings.append("stuck_jobs_detected")

        return {
            "healthy": len(warnings) == 0,
            "total_jobs": total_jobs,
            "active_jobs": active_jobs,
            "active_timers": active_timers,
            "stuck_jobs": stuck_jobs,
            "warnings": warnings
        }

    async def _simulate_crawl(self, job: CrawlJob) -> None:
        """Simulate the crawling process with comprehensive logging."""
        crawl_timer_id = self._start_operation_timer("simulate_crawl", job_id=job.job_id)

        try:
            # Log state transition to running
            self.enhanced_logger.log_state_transition(
                "job", job.job_id, job.status.value, CrawlStatus.RUNNING.value
            )
            job.status = CrawlStatus.RUNNING

            # Create data generator with job configuration
            config = SimWeaverConfig(
                max_pages=job.max_pages,
                max_depth=job.max_depth,
                delay_min=job.delay_min,
                delay_max=job.delay_max,
                failure_rate=job.failure_rate
            )
            data_generator = DataGenerator(config)

            self.enhanced_logger.debug(
                "Crawl simulation started",
                job_id=job.job_id,
                name=job.name,
                simulation_config=config.to_dict(),
                initial_pages=len(job.pages)
            )

            pages_processed = 0
            last_metrics_log = 0

            while not job._should_stop and job.stats.pages_crawled < job.max_pages:
                # Handle pause
                if job._is_paused:
                    self.enhanced_logger.debug(
                        "Job paused, waiting for resume",
                        job_id=job.job_id
                    )
                    while job._is_paused and not job._should_stop:
                        await asyncio.sleep(0.5)

                    if not job._should_stop:
                        self.enhanced_logger.debug(
                            "Job resumed from pause",
                            job_id=job.job_id
                        )

                if job._should_stop:
                    self.enhanced_logger.debug(
                        "Job stop requested, breaking crawl loop",
                        job_id=job.job_id,
                        pages_processed=pages_processed
                    )
                    break

                # Find next page to crawl
                next_page = self._get_next_page_to_crawl(job)
                if not next_page:
                    self.enhanced_logger.debug(
                        "No more pages to crawl",
                        job_id=job.job_id,
                        total_pages=len(job.pages),
                        pages_crawled=job.stats.pages_crawled
                    )
                    break

                # Simulate crawling the page
                page_timer_id = self._start_operation_timer(
                    "crawl_page",
                    job_id=job.job_id,
                    url=next_page.url
                )

                await self._crawl_page(job, next_page, data_generator)

                self._end_operation_timer(
                    page_timer_id,
                    "crawl_page",
                    job_id=job.job_id,
                    url=next_page.url,
                    status=next_page.status.value
                )

                pages_processed += 1

                # Update statistics
                self._update_job_stats(job)

                # Log metrics periodically
                if (pages_processed - last_metrics_log >= self.logging_config.metrics_log_interval):
                    self._log_crawl_metrics(job)
                    last_metrics_log = pages_processed

                # Random delay between requests (sped up for simulation)
                delay = random.uniform(job.delay_min, job.delay_max)
                await asyncio.sleep(delay / config.simulation_speed)

            # Job completed successfully
            if not job._should_stop:
                self.enhanced_logger.log_state_transition(
                    "job", job.job_id, job.status.value, CrawlStatus.COMPLETED.value
                )

                job.status = CrawlStatus.COMPLETED
                job.completed_at = datetime.now()
                job.stats.end_time = job.completed_at

                if job.stats.start_time:
                    job.stats.duration = (job.completed_at - job.stats.start_time).total_seconds()

                # Final metrics log
                self._log_crawl_completion(job)

                log_job_event(
                    job_id=job.job_id,
                    event_type="job_completed",
                    pages_crawled=job.stats.pages_crawled,
                    duration=job.stats.duration
                )

            self.active_jobs.discard(job.job_id)
            self._end_operation_timer(crawl_timer_id, "simulate_crawl", job_id=job.job_id, success=True)

        except Exception as e:
            self.enhanced_logger.log_state_transition(
                "job", job.job_id, job.status.value, CrawlStatus.FAILED.value
            )

            job.status = CrawlStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.now()
            self.active_jobs.discard(job.job_id)

            self.enhanced_logger.error(
                "Crawl simulation failed",
                job_id=job.job_id,
                name=job.name,
                error=str(e),
                error_type=type(e).__name__,
                pages_processed=job.stats.pages_crawled
            )

            log_job_event(
                job_id=job.job_id,
                event_type="job_failed",
                error=str(e)
            )

            self._end_operation_timer(crawl_timer_id, "simulate_crawl", job_id=job.job_id, success=False, error=str(e))

    def _log_crawl_metrics(self, job: CrawlJob):
        """Log current crawl metrics."""
        metrics = {
            "pages_total": job.stats.total_pages,
            "pages_crawled": job.stats.pages_crawled,
            "pages_pending": job.stats.pages_pending,
            "pages_failed": job.stats.pages_failed,
            "pages_per_second": job.stats.pages_per_second,
            "avg_response_time": job.stats.avg_response_time,
            "data_downloaded_mb": job.stats.total_data_downloaded / (1024 * 1024),
            "unique_domains": job.stats.unique_domains,
            "success_rate": job.get_success_rate(),
            "progress_percentage": job.get_progress_percentage(),
            "estimated_time_remaining": job.get_estimated_time_remaining()
        }

        self.enhanced_logger.log_metrics_snapshot(job.job_id, metrics)

        # Log performance warnings
        if job.stats.pages_per_second < 0.1:
            self.enhanced_logger.performance(
                "Low crawl rate detected",
                job_id=job.job_id,
                pages_per_second=job.stats.pages_per_second,
                warning_threshold=0.1
            )

        if job.stats.avg_response_time > 5.0:
            self.enhanced_logger.performance(
                "High average response time",
                job_id=job.job_id,
                avg_response_time=job.stats.avg_response_time,
                warning_threshold=5.0
            )

    def _log_crawl_completion(self, job: CrawlJob):
        """Log comprehensive completion metrics."""
        completion_metrics = {
            "job_id": job.job_id,
            "name": job.name,
            "duration_seconds": job.stats.duration,
            "pages_crawled": job.stats.pages_crawled,
            "pages_failed": job.stats.pages_failed,
            "success_rate": job.get_success_rate(),
            "avg_pages_per_second": job.stats.pages_per_second,
            "avg_response_time": job.stats.avg_response_time,
            "total_data_mb": job.stats.total_data_downloaded / (1024 * 1024),
            "unique_domains": job.stats.unique_domains,
            "max_depth_reached": max((page.depth for page in job.pages.values()), default=0)
        }

        self.enhanced_logger.performance(
            "Crawl job completed successfully",
            **completion_metrics
        )

        # Log efficiency metrics
        if job.stats.duration and job.stats.duration > 0:
            efficiency_score = job.stats.pages_crawled / job.stats.duration
            self.enhanced_logger.metrics(
                "Crawl efficiency analysis",
                job_id=job.job_id,
                efficiency_score=efficiency_score,
                pages_per_second=job.stats.pages_per_second,
                time_per_page=job.stats.duration / max(job.stats.pages_crawled, 1)
            )

    def _get_next_page_to_crawl(self, job: CrawlJob) -> Optional[CrawlPage]:
        """Get the next page that needs to be crawled."""
        for page in job.pages.values():
            if page.status == PageStatus.PENDING:
                return page
        return None

    async def _crawl_page(self, job: CrawlJob, page: CrawlPage, data_generator: DataGenerator) -> None:
        """Simulate crawling a single page with comprehensive logging."""
        # Log state transition
        self.enhanced_logger.log_state_transition(
            "page", page.url, page.status.value, PageStatus.CRAWLING.value
        )

        page.status = PageStatus.CRAWLING
        page.crawled_at = datetime.now()

        # Log network request
        self.enhanced_logger.log_network_request(
            page.url,
            method="GET",
            depth=page.depth,
            parent_url=page.parent_url,
            job_id=job.job_id
        )

        # Determine if this page should be slow
        is_slow = data_generator.should_page_be_slow()
        if is_slow:
            self.enhanced_logger.debug(
                "Simulating slow page response",
                url=page.url,
                job_id=job.job_id,
                is_slow=True
            )

        # Simulate network delay
        crawl_time = data_generator.generate_crawl_time(is_slow)
        await asyncio.sleep(crawl_time / data_generator.config.simulation_speed)

        page.crawl_time = crawl_time

        # Determine page outcome and log accordingly
        if data_generator.should_page_timeout():
            page.status = PageStatus.TIMEOUT
            page.error_message = "Request timeout"
            job.stats.pages_failed += 1

            self.enhanced_logger.log_simulated_error(
                "timeout",
                page.url,
                job_id=job.job_id,
                crawl_time=crawl_time,
                timeout_threshold=30.0
            )

            self.enhanced_logger.log_network_response(
                page.url,
                status_code=0,
                response_time=crawl_time,
                error="timeout",
                job_id=job.job_id
            )

        elif data_generator.should_page_be_blocked():
            page.status = PageStatus.BLOCKED
            page.status_code = 403
            page.error_message = "Access blocked"
            job.stats.pages_failed += 1

            self.enhanced_logger.log_simulated_error(
                "blocked",
                page.url,
                job_id=job.job_id,
                status_code=403,
                reason="access_denied"
            )

            # Simulate security event
            self.enhanced_logger.log_security_event(
                "access_blocked",
                url=page.url,
                job_id=job.job_id,
                status_code=403
            )

            self.enhanced_logger.log_network_response(
                page.url,
                status_code=403,
                response_time=crawl_time,
                job_id=job.job_id
            )

        elif data_generator.should_page_fail():
            # Various types of failures
            status_code, error_msg = data_generator.generate_error_status_code()
            page.status = PageStatus.ERROR
            page.status_code = status_code
            page.error_message = error_msg
            job.stats.pages_failed += 1

            self.enhanced_logger.log_simulated_error(
                "http_error",
                page.url,
                job_id=job.job_id,
                status_code=status_code,
                error_message=error_msg,
                crawl_time=crawl_time
            )

            self.enhanced_logger.log_network_response(
                page.url,
                status_code=status_code,
                response_time=crawl_time,
                error=error_msg,
                job_id=job.job_id
            )

        else:
            # Successful crawl
            page.status = PageStatus.SUCCESS
            page.status_code = data_generator.generate_success_status_code()
            page.content_length = data_generator.generate_content_length()
            job.stats.pages_crawled += 1
            job.stats.total_data_downloaded += page.content_length

            self.enhanced_logger.log_network_response(
                page.url,
                status_code=page.status_code,
                response_time=crawl_time,
                content_length=page.content_length,
                job_id=job.job_id
            )

            self.enhanced_logger.debug(
                "Page crawled successfully",
                url=page.url,
                job_id=job.job_id,
                status_code=page.status_code,
                content_length=page.content_length,
                crawl_time=crawl_time,
                depth=page.depth
            )

            # Generate new pages to crawl (if within depth limit)
            if page.depth < job.max_depth:
                child_urls = data_generator.generate_child_urls(page.url, page.depth)

                self.enhanced_logger.trace(
                    "Generating child URLs",
                    parent_url=page.url,
                    job_id=job.job_id,
                    child_count=len(child_urls),
                    current_depth=page.depth,
                    max_depth=job.max_depth
                )

                for child_url in child_urls:
                    if child_url not in job.pages and len(job.pages) < job.max_pages:
                        child_page = CrawlPage(
                            url=child_url,
                            depth=page.depth + 1,
                            parent_url=page.url
                        )
                        job.pages[child_url] = child_page
                        job.stats.total_pages += 1
                        job.stats.pages_pending += 1

                        # Log page discovery
                        self.enhanced_logger.log_page_discovered(
                            child_url,
                            page.depth + 1,
                            page.url
                        )

        # Log state transition to final status
        self.enhanced_logger.log_state_transition(
            "page", page.url, PageStatus.CRAWLING.value, page.status.value
        )

        # Update pending count
        job.stats.pages_pending = sum(
            1 for p in job.pages.values()
            if p.status == PageStatus.PENDING
        )

    def _update_job_stats(self, job: CrawlJob) -> None:
        """Update job statistics."""
        if job.stats.start_time:
            elapsed = (datetime.now() - job.stats.start_time).total_seconds()
            if elapsed > 0:
                job.stats.pages_per_second = job.stats.pages_crawled / elapsed

        # Calculate average response time
        crawl_times = [
            page.crawl_time for page in job.pages.values()
            if page.crawl_time is not None
        ]

        if crawl_times:
            job.stats.avg_response_time = sum(crawl_times) / len(crawl_times)

        # Count unique domains
        domains = set()
        for page in job.pages.values():
            try:
                from urllib.parse import urlparse
                domain = urlparse(page.url).netloc
                domains.add(domain)
            except Exception:
                pass

        job.stats.unique_domains = len(domains)
