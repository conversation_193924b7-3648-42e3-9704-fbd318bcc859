# How to Create a Crawl Logging Feature with File Viewer

**Date**: 2025-08-30  
**Project**: SpigaUI  
**Feature**: Crawl Log File Viewer with Reset Functionality  
**Framework**: React + TypeScript + FastAPI + Tailwind CSS

---

## 🎯 **Overview**

This guide documents the implementation of a comprehensive crawl logging feature that enhances the existing real-time logs page with:

- **Tabbed Interface**: Real-time logs + Historical log files
- **File Viewer**: Read and search through log files on disk
- **Reset Functionality**: Clear all crawl log files with confirmation
- **Smart Parsing**: JSON log detection and formatting
- **Professional UI**: Consistent with existing design patterns

### **Key Features Implemented**
- ✅ Backend API endpoints for file operations
- ✅ Frontend tabbed interface with file selection
- ✅ Search and filtering capabilities
- ✅ Reset functionality with safety confirmations
- ✅ Toast notifications instead of alerts
- ✅ Responsive design and loading states

---

## 📁 **Files Modified**

### **Backend Changes**
- `spigaui/api/crawl.py` - Added log file endpoints and reset functionality

### **Frontend Changes**
- `frontend/src/lib/api.ts` - Added API client functions and TypeScript types
- `frontend/src/pages/Logs.tsx` - Enhanced with tabbed interface and file viewer

### **No Additional Dependencies Required**
- Used existing React hooks and libraries
- Used existing Tailwind CSS classes
- Used existing FastAPI patterns

---

## 🛠️ **Implementation Steps**

### **Step 1: Backend API Endpoints**

**Location**: `spigaui/api/crawl.py`

#### **1.1 Add Log Files Listing Endpoint**

```python
@router.get("/crawl/logging/files", response_model=Dict[str, Any])
async def list_log_files() -> Dict[str, Any]:
    """List available log files."""
    from pathlib import Path
    
    log_dir = Path("logs")
    if not log_dir.exists():
        return {"files": [], "message": "No log directory found"}
    
    files = []
    for log_file in log_dir.glob("*.log"):
        stat = log_file.stat()
        files.append({
            "name": log_file.name,
            "size": stat.st_size,
            "modified": stat.st_mtime,
            "path": str(log_file)
        })
    
    return {
        "files": sorted(files, key=lambda x: x["modified"], reverse=True),
        "message": f"Found {len(files)} log files"
    }
```

**Key Features**:
- **Security**: Only scans `logs/` directory for `.log` files
- **Metadata**: Returns file size and modification time
- **Sorting**: Most recently modified files first

#### **1.2 Add Log File Reading Endpoint**

```python
@router.get("/crawl/logging/files/{filename}", response_model=Dict[str, Any])
async def read_crawl_log_file(
    filename: str,
    lines: int = 100,
    offset: int = 0,
    search: str = None
) -> Dict[str, Any]:
    """Read content from a crawl log file."""
    from pathlib import Path
    import json
    
    log_file = Path("logs") / filename
    
    # Security validation
    if not log_file.exists() or not log_file.suffix == ".log" or not str(log_file).startswith("logs"):
        raise HTTPException(status_code=404, detail="Crawl log file not found")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
        
        # Apply search filter
        if search:
            all_lines = [line for line in all_lines if search.lower() in line.lower()]
        
        # Pagination logic
        total_lines = len(all_lines)
        start_idx = max(0, total_lines - lines - offset)
        end_idx = total_lines - offset if offset > 0 else total_lines
        selected_lines = all_lines[start_idx:end_idx]
        
        # Smart JSON parsing
        parsed_lines = []
        for line_num, line in enumerate(selected_lines, start=start_idx + 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                parsed = json.loads(line)
                parsed_lines.append({
                    "line_number": line_num,
                    "raw": line,
                    "parsed": parsed,
                    "is_json": True
                })
            except json.JSONDecodeError:
                parsed_lines.append({
                    "line_number": line_num,
                    "raw": line,
                    "parsed": None,
                    "is_json": False
                })
        
        return {
            "filename": filename,
            "total_lines": total_lines,
            "returned_lines": len(parsed_lines),
            "lines": parsed_lines,
            "search": search,
            "offset": offset
        }
        
    except Exception as e:
        logger.error("Failed to read crawl log file", filename=filename, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to read crawl log file: {str(e)}")
```

**Key Features**:
- **Pagination**: `lines` and `offset` parameters for large files
- **Search**: Filter lines containing search term
- **Smart Parsing**: Detects JSON vs plain text lines
- **Security**: Path validation and error handling

#### **1.3 Add Reset Functionality**

```python
@router.post("/crawl/logging/reset", response_model=Dict[str, str])
async def reset_crawl_logs() -> Dict[str, str]:
    """Reset (clear) all crawl log files."""
    from pathlib import Path
    
    logger.warning("Resetting crawl logs - clearing all log files")
    
    try:
        log_dir = Path("logs")
        if not log_dir.exists():
            return {"message": "No log directory found - nothing to reset"}
        
        cleared_files = []
        for log_file in log_dir.glob("*.log"):
            try:
                # Truncate file (safer than deletion)
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.truncate(0)
                cleared_files.append(log_file.name)
                logger.info("Cleared crawl log file", filename=log_file.name)
            except Exception as e:
                logger.error("Failed to clear crawl log file", filename=log_file.name, error=str(e))
        
        if cleared_files:
            message = f"Successfully reset {len(cleared_files)} crawl log files: {', '.join(cleared_files)}"
        else:
            message = "No crawl log files found to reset"
        
        logger.info("Crawl log reset completed", cleared_files=cleared_files)
        return {"message": message}
        
    except Exception as e:
        logger.error("Failed to reset crawl logs", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to reset crawl logs: {str(e)}")
```

**Key Features**:
- **Safety**: Truncates files instead of deleting them
- **Logging**: Comprehensive audit trail
- **Error Handling**: Graceful failure with detailed messages

### **Step 2: Frontend API Client**

**Location**: `frontend/src/lib/api.ts`

#### **2.1 Add TypeScript Interfaces**

```typescript
export interface LogFile {
  name: string
  size: number
  modified: number
  path: string
}

export interface LogFileLine {
  line_number: number
  raw: string
  parsed: any | null
  is_json: boolean
}

export interface LogFileContent {
  filename: string
  total_lines: number
  returned_lines: number
  lines: LogFileLine[]
  search?: string
  offset: number
}
```

#### **2.2 Add API Functions**

```typescript
export const loggingApi = {
  // ... existing functions ...

  // Get list of available crawl log files
  getCrawlLogFiles: (): Promise<{ files: LogFile[], message: string }> =>
    api.get('/crawl/logging/files').then(res => res.data),

  // Read content from a specific crawl log file
  readCrawlLogFile: (
    filename: string,
    lines: number = 100,
    offset: number = 0,
    search?: string
  ): Promise<LogFileContent> => {
    const params = new URLSearchParams({
      lines: lines.toString(),
      offset: offset.toString(),
      ...(search && { search })
    })
    return api.get(`/crawl/logging/files/${filename}?${params}`).then(res => res.data)
  },

  // Reset (clear) all crawl log files
  resetCrawlLogs: (): Promise<{ message: string }> =>
    api.post('/crawl/logging/reset').then(res => res.data),
}
```

### **Step 3: Frontend UI Implementation**

**Location**: `frontend/src/pages/Logs.tsx`

#### **3.1 Add State Management**

```typescript
const Logs: React.FC = () => {
  const { showSuccess, showError } = useToast()
  
  // Tab state
  const [activeTab, setActiveTab] = useState<'realtime' | 'files'>('realtime')
  
  // File logs state
  const [selectedLogFile, setSelectedLogFile] = useState<string>('')
  const [fileSearchTerm, setFileSearchTerm] = useState<string>('')
  const [fileLines, setFileLines] = useState<number>(100)
  const [fileOffset, setFileOffset] = useState<number>(0)
  
  // ... existing real-time logs state ...
}
```

#### **3.2 Add React Query Hooks**

```typescript
// Get available crawl log files
const { data: logFiles, isLoading: logFilesLoading, refetch: refetchLogFiles } = useQuery({
  queryKey: ['crawl-log-files'],
  queryFn: loggingApi.getCrawlLogFiles,
  refetchInterval: 30000, // Refresh every 30 seconds
})

// Get crawl log file content
const { data: logFileContent, isLoading: logFileContentLoading, refetch: refetchLogFile } = useQuery({
  queryKey: ['crawl-log-file-content', selectedLogFile, fileLines, fileOffset, fileSearchTerm],
  queryFn: () => loggingApi.readCrawlLogFile(selectedLogFile, fileLines, fileOffset, fileSearchTerm || undefined),
  enabled: !!selectedLogFile && activeTab === 'files',
})

// Reset crawl logs mutation
const resetLogsMutation = useMutation({
  mutationFn: loggingApi.resetCrawlLogs,
  onSuccess: (data) => {
    showSuccess(data.message)
    setSelectedLogFile('') // Clear selection
    refetchLogFiles() // Refresh file list
  },
  onError: () => {
    showError('Failed to reset crawl logs')
  },
})
```

#### **3.3 Create Tabbed Interface**

```typescript
{/* Tab Navigation */}
<div className="mb-6">
  <div className="border-b border-gray-700">
    <nav className="-mb-px flex space-x-8">
      <button
        onClick={() => setActiveTab('realtime')}
        className={`py-2 px-1 border-b-2 font-medium text-sm ${
          activeTab === 'realtime'
            ? 'border-blue-500 text-blue-400'
            : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
        }`}
      >
        📡 Real-time Logs
      </button>
      <button
        onClick={() => setActiveTab('files')}
        className={`py-2 px-1 border-b-2 font-medium text-sm ${
          activeTab === 'files'
            ? 'border-blue-500 text-blue-400'
            : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
        }`}
      >
        📁 Crawl Log Files
      </button>
    </nav>
  </div>
</div>
```

#### **3.4 Create Reset Button with Confirmation**

```typescript
<button
  onClick={() => {
    if (confirm('Are you sure you want to reset all crawl logs? This will permanently clear all log file contents and cannot be undone.')) {
      resetLogsMutation.mutate()
    }
  }}
  disabled={resetLogsMutation.isPending}
  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
>
  {resetLogsMutation.isPending ? 'Resetting...' : '🗑️ Reset Crawl Logs'}
</button>
```

### **Step 4: Toast Integration**

**Location**: `frontend/src/pages/Logs.tsx`

#### **4.1 Import Toast Hook**

```typescript
import { useToast } from '../hooks/useToast'

const Logs: React.FC = () => {
  const { showSuccess, showError } = useToast()
  // ...
}
```

#### **4.2 Update All Mutations**

```typescript
// Replace all alert() calls with toast notifications
const setPresetMutation = useMutation({
  mutationFn: loggingApi.setPreset,
  onSuccess: (data) => {
    showSuccess(data.message)  // Instead of alert(data.message)
    setLogs([])
  },
  onError: () => {
    showError('Failed to set logging preset')  // Instead of alert('Failed...')
  },
})
```

---

## 🎨 **Design Principles Applied**

### **1. Progressive Enhancement**
- **Existing Functionality**: Real-time logs remain unchanged
- **New Capability**: File viewer adds historical access
- **Seamless Integration**: Tabbed interface feels natural

### **2. Security First**
- **Path Validation**: Only access files in `logs/` directory
- **File Type Restriction**: Only `.log` files allowed
- **Input Sanitization**: Search terms and parameters validated

### **3. User Experience**
- **Confirmation Dialogs**: Prevent accidental data loss
- **Loading States**: Clear feedback during operations
- **Error Handling**: Graceful failure with helpful messages
- **Responsive Design**: Works on all screen sizes

### **4. Performance Optimization**
- **Pagination**: Handle large log files efficiently
- **Caching**: React Query caches API responses
- **Lazy Loading**: File content only loads when needed

---

## 🧪 **Testing Approach**

### **Manual Testing Checklist**
- [ ] Tab switching works smoothly
- [ ] File selection populates dropdown with sizes
- [ ] Search filtering works correctly
- [ ] Pagination (lines/offset) functions properly
- [ ] JSON logs display with proper formatting
- [ ] Plain text logs display correctly
- [ ] Reset functionality clears files
- [ ] Confirmation dialog prevents accidents
- [ ] Toast notifications appear for all actions
- [ ] Loading states show during operations
- [ ] Error handling works for invalid files
- [ ] Responsive design works on mobile

### **API Testing**
```bash
# Test file listing
curl http://localhost:8000/api/crawl/logging/files

# Test file reading
curl "http://localhost:8000/api/crawl/logging/files/spigaui.log?lines=10"

# Test search
curl "http://localhost:8000/api/crawl/logging/files/spigaui.log?search=error"

# Test reset (destructive!)
curl -X POST http://localhost:8000/api/crawl/logging/reset
```

---

## 🚀 **Results Achieved**

### **User Experience Improvements**
- **Historical Access**: Users can now view past log entries
- **Search Capability**: Find specific events or errors quickly
- **File Management**: Easy reset for testing and maintenance
- **Professional UI**: Consistent with existing design patterns

### **Technical Benefits**
- **Scalable Architecture**: Easy to extend with more file operations
- **Type Safety**: Full TypeScript coverage for all new features
- **Error Resilience**: Comprehensive error handling and recovery
- **Performance**: Efficient pagination for large files

### **Operational Benefits**
- **Debugging**: Historical logs help investigate past issues
- **Testing**: Reset functionality enables clean test environments
- **Maintenance**: File size monitoring and management
- **Audit Trail**: Complete logging of all operations

---

## 🔄 **Potential Enhancements**

### **Future Improvements**
1. **Download Functionality**: Export log files as downloads
2. **Real-time File Updates**: Auto-refresh when files change
3. **Advanced Filtering**: Date ranges, log levels, categories
4. **Log Rotation**: Automatic archiving of old logs
5. **Compression**: Gzip support for large files
6. **Syntax Highlighting**: Better JSON formatting

### **Performance Optimizations**
- **Streaming**: Server-sent events for large file reading
- **Virtualization**: Handle very large files efficiently
- **Caching**: Browser-side caching for frequently accessed files

---

## 📚 **Key Learnings**

### **What Worked Well**
- **Incremental Development**: Building on existing patterns
- **TypeScript**: Caught many potential runtime errors
- **React Query**: Simplified state management and caching
- **Tailwind CSS**: Rapid UI development with consistent styling

### **Challenges Overcome**
- **File Security**: Ensuring safe file access without vulnerabilities
- **Large Files**: Implementing efficient pagination for performance
- **State Management**: Coordinating multiple UI states and API calls
- **User Safety**: Preventing accidental data loss with confirmations

### **Best Practices Demonstrated**
- **API Design**: RESTful endpoints with clear responsibilities
- **Error Handling**: Comprehensive error states and user feedback
- **Security**: Input validation and path restrictions
- **UX Design**: Progressive disclosure and confirmation patterns

---

**🎉 Implementation Complete**: Professional crawl log file viewer with reset functionality, seamlessly integrated into existing UI!
