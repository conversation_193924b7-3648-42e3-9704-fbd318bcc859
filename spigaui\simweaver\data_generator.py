"""
SimWeaver data generation utilities.

Generates realistic URLs, domains, and page content for crawler simulation.
"""

import random
import string
from typing import List, Set, Optional
from urllib.parse import urlparse, urljoin

from .config import SimWeaverConfig
from .models import CrawlPage, PageStatus


class DataGenerator:
    """Generates realistic crawl data for simulation."""
    
    def __init__(self, config: SimWeaverConfig):
        self.config = config
        
        # Realistic domain names
        self.base_domains = [
            "example.com", "test-site.org", "demo.net", "sample.io",
            "mocksite.dev", "placeholder.co", "testdomain.info",
            "demosite.com", "sampleapp.org", "testportal.net",
            "mockapi.dev", "placeholdersite.io", "demoweb.co"
        ]
        
        # Common path segments
        self.path_segments = [
            "about", "products", "services", "contact", "blog", "news",
            "help", "support", "docs", "api", "download", "pricing",
            "features", "team", "careers", "privacy", "terms", "faq",
            "gallery", "portfolio", "testimonials", "resources", "tools",
            "dashboard", "profile", "settings", "admin", "login", "signup"
        ]
        
        # File extensions and types
        self.file_extensions = [
            ".html", ".php", ".asp", ".jsp", ".htm", ".shtml",
            "", "/", ".do", ".action", ".cfm"
        ]
        
        # Subdomain prefixes
        self.subdomain_prefixes = [
            "www", "blog", "shop", "api", "admin", "support", "docs",
            "dev", "staging", "test", "mobile", "m", "app", "portal"
        ]
        
        # Common query parameters
        self.query_params = [
            "id", "page", "category", "tag", "search", "q", "filter",
            "sort", "limit", "offset", "lang", "locale", "theme"
        ]
        
        # HTTP status codes and their probabilities
        self.success_status_codes = [200, 201, 202, 204]
        self.error_status_codes = [
            (404, "Not Found"),
            (500, "Internal Server Error"),
            (403, "Forbidden"),
            (401, "Unauthorized"),
            (503, "Service Unavailable"),
            (502, "Bad Gateway"),
            (408, "Request Timeout"),
            (429, "Too Many Requests")
        ]
    
    def generate_child_urls(self, parent_url: str, depth: int) -> List[str]:
        """Generate realistic child URLs for a parent page."""
        if depth >= self.config.max_depth:
            return []
        
        try:
            parsed = urlparse(parent_url)
            base_domain = f"{parsed.scheme}://{parsed.netloc}"
        except Exception:
            base_domain = "https://example.com"
        
        num_children = random.randint(
            self.config.min_links_per_page,
            self.config.max_links_per_page
        )
        
        child_urls = []
        used_paths = set()
        
        for _ in range(num_children):
            # Generate unique path
            path = self._generate_unique_path(used_paths)
            used_paths.add(path)
            
            # Add query parameters sometimes
            if random.random() < 0.2:
                path += self._generate_query_string()
            
            child_url = base_domain + path
            child_urls.append(child_url)
        
        return child_urls
    
    def _generate_unique_path(self, used_paths: Set[str]) -> str:
        """Generate a unique URL path."""
        max_attempts = 10
        
        for _ in range(max_attempts):
            # Generate path components
            num_segments = random.randint(1, 3)
            segments = random.sample(self.path_segments, min(num_segments, len(self.path_segments)))
            
            # Sometimes add numeric IDs
            if random.random() < 0.3:
                segments.append(str(random.randint(1, 9999)))
            
            path = "/" + "/".join(segments)
            
            # Add file extension sometimes
            if random.random() < 0.3:
                path += random.choice(self.file_extensions)
            
            if path not in used_paths:
                return path
        
        # Fallback: generate random path
        random_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        return f"/page-{random_id}"
    
    def _generate_query_string(self) -> str:
        """Generate a realistic query string."""
        num_params = random.randint(1, 3)
        params = []
        
        for _ in range(num_params):
            param_name = random.choice(self.query_params)
            
            # Generate appropriate value based on parameter name
            if param_name in ["id", "page", "limit", "offset"]:
                value = str(random.randint(1, 100))
            elif param_name in ["search", "q"]:
                value = random.choice(["test", "example", "demo", "sample"])
            elif param_name in ["category", "tag", "filter"]:
                value = random.choice(["news", "tech", "business", "sports"])
            elif param_name in ["sort"]:
                value = random.choice(["date", "name", "popularity", "rating"])
            else:
                value = random.choice(["true", "false", "1", "0", "yes", "no"])
            
            params.append(f"{param_name}={value}")
        
        return "?" + "&".join(params)
    
    def generate_domain(self) -> str:
        """Generate a realistic domain name."""
        if self.config.custom_domains:
            base_domain = random.choice(self.config.custom_domains)
        else:
            base_domain = random.choice(self.base_domains)
        
        # Add subdomain sometimes
        if self.config.generate_subdomains and random.random() < 0.3:
            subdomain = random.choice(self.subdomain_prefixes)
            return f"{subdomain}.{base_domain}"
        
        return base_domain
    
    def generate_content_length(self) -> int:
        """Generate realistic content length."""
        return random.randint(
            self.config.min_content_size,
            self.config.max_content_size
        )
    
    def generate_crawl_time(self, is_slow: bool = False) -> float:
        """Generate realistic crawl time."""
        if is_slow:
            # Slow pages take longer
            return random.uniform(
                self.config.slow_page_threshold,
                self.config.slow_page_threshold * 3
            )
        else:
            # Normal pages
            return random.uniform(0.1, self.config.slow_page_threshold)
    
    def should_page_fail(self) -> bool:
        """Determine if a page should fail."""
        return random.random() < self.config.failure_rate
    
    def should_page_timeout(self) -> bool:
        """Determine if a page should timeout."""
        return random.random() < self.config.timeout_rate
    
    def should_page_be_blocked(self) -> bool:
        """Determine if a page should be blocked."""
        return random.random() < self.config.blocked_rate
    
    def should_page_be_slow(self) -> bool:
        """Determine if a page should be slow."""
        return (self.config.simulate_slow_pages and 
                random.random() < self.config.slow_page_rate)
    
    def generate_success_status_code(self) -> int:
        """Generate a success HTTP status code."""
        return random.choice(self.success_status_codes)
    
    def generate_error_status_code(self) -> tuple[int, str]:
        """Generate an error HTTP status code and message."""
        return random.choice(self.error_status_codes)
    
    def generate_page_title(self, url: str) -> str:
        """Generate a realistic page title based on URL."""
        try:
            parsed = urlparse(url)
            path_parts = [p for p in parsed.path.split('/') if p]
            
            if path_parts:
                # Use last path segment as basis for title
                last_segment = path_parts[-1].replace('-', ' ').replace('_', ' ')
                # Remove file extensions
                if '.' in last_segment:
                    last_segment = last_segment.split('.')[0]
                title = last_segment.title()
            else:
                title = "Home"
            
            # Add domain name
            domain = parsed.netloc.replace('www.', '')
            return f"{title} - {domain.title()}"
            
        except Exception:
            return "Sample Page - Example Site"
