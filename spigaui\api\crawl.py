"""
Crawl management endpoints for SpigaUI.

Handles starting, stopping, and monitoring crawl jobs using SimWeaver.
"""

from typing import Dict, Any, List, Optional
from uuid import uuid4

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, HttpUrl
import structlog

from spigaui.models.crawl import (
    CrawlJobCreate, CrawlJobSummary, CrawlJobDetail,
    SimWeaverConfigAPI, CrawlJobStart, CrawlStats, CrawlPage
)
from spigaui.simweaver.config import SimWeaverConfig
from spigaui.simweaver.logging_config import SimWeaverLoggingConfig, LogCategory
from spigaui.simweaver import simweaver
from spigaui.core.logging import log_job_event

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.post("/crawl", response_model=Dict[str, str])
async def create_crawl_job(request: CrawlJobCreate) -> Dict[str, str]:
    """
    Create a new crawl job.

    Creates a new crawl job with the specified configuration but doesn't start it.
    """
    logger.info(
        "Creating crawl job",
        name=request.name,
        start_url=request.start_url,
        config=request.config.model_dump() if request.config else None
    )

    try:
        # Use default config if none provided
        if request.config:
            config = SimWeaverConfig(
                max_pages=request.config.max_pages,
                max_depth=request.config.max_depth,
                delay_min=request.config.delay_min,
                delay_max=request.config.delay_max,
                failure_rate=request.config.failure_rate
            )
        else:
            config = SimWeaverConfig()

        # Create the job using SimWeaver
        job_id = simweaver.create_job(
            name=request.name,
            start_url=request.start_url,
            config=config
        )

        logger.info("Crawl job created successfully", job_id=job_id, name=request.name)

        return {
            "job_id": job_id,
            "message": f"Crawl job '{request.name}' created successfully"
        }

    except Exception as e:
        logger.error("Failed to create crawl job", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create crawl job: {str(e)}"
        )


@router.post("/crawl/{job_id}/start", response_model=Dict[str, str])
async def start_crawl_job(job_id: str) -> Dict[str, str]:
    """
    Start a crawl job.

    Starts a previously created crawl job.
    """
    logger.info("Starting crawl job", job_id=job_id)

    try:
        success = await simweaver.start_job(job_id)

        if not success:
            raise HTTPException(
                status_code=400,
                detail=f"Could not start crawl job {job_id}. Job may not exist or may not be in queued state."
            )

        logger.info("Crawl job started successfully", job_id=job_id)

        return {
            "job_id": job_id,
            "message": f"Crawl job {job_id} started successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to start crawl job", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start crawl job: {str(e)}"
        )


@router.get("/crawl/{job_id}", response_model=CrawlJobDetail)
async def get_crawl_job(job_id: str) -> CrawlJobDetail:
    """
    Get crawl job status and details.

    Returns the current status, progress, and results of a crawl job.
    """
    logger.debug("Getting crawl job status", job_id=job_id)

    try:
        job = simweaver.get_job(job_id)

        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Crawl job {job_id} not found"
            )

        # Convert SimWeaver job to API response model
        pages = [
            CrawlPage(
                url=page.url,
                status=page.status.value,
                status_code=page.status_code,
                content_length=page.content_length,
                crawl_time=page.crawl_time,
                error_message=page.error_message,
                discovered_at=page.discovered_at,
                crawled_at=page.crawled_at,
                depth=page.depth,
                parent_url=page.parent_url
            )
            for page in job.pages.values()
        ]

        stats = CrawlStats(
            total_pages=job.stats.total_pages,
            pages_crawled=job.stats.pages_crawled,
            pages_pending=job.stats.pages_pending,
            pages_failed=job.stats.pages_failed,
            pages_per_second=job.stats.pages_per_second,
            avg_response_time=job.stats.avg_response_time,
            total_data_downloaded=job.stats.total_data_downloaded,
            unique_domains=job.stats.unique_domains,
            start_time=job.stats.start_time,
            end_time=job.stats.end_time,
            duration=job.stats.duration
        )

        return CrawlJobDetail(
            job_id=job.job_id,
            name=job.name,
            start_url=job.start_url,
            status=job.status.value,
            created_at=job.created_at,
            started_at=job.started_at,
            completed_at=job.completed_at,
            error_message=job.error_message,
            max_pages=job.max_pages,
            max_depth=job.max_depth,
            delay_min=job.delay_min,
            delay_max=job.delay_max,
            failure_rate=job.failure_rate,
            stats=stats,
            pages=pages
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get crawl job", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get crawl job: {str(e)}"
        )


@router.post("/crawl/{job_id}/stop", response_model=Dict[str, str])
async def stop_crawl_job(job_id: str) -> Dict[str, str]:
    """
    Stop a running crawl job.

    Attempts to stop a crawl job if it's currently running.
    """
    logger.info("Stopping crawl job", job_id=job_id)

    try:
        success = await simweaver.stop_job(job_id)

        if not success:
            raise HTTPException(
                status_code=400,
                detail=f"Could not stop crawl job {job_id}. Job may not exist or may not be running."
            )

        logger.info("Crawl job stopped successfully", job_id=job_id)

        return {
            "job_id": job_id,
            "message": f"Crawl job {job_id} stopped successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to stop crawl job", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to stop crawl job: {str(e)}"
        )


@router.post("/crawl/{job_id}/pause", response_model=Dict[str, str])
async def pause_crawl_job(job_id: str) -> Dict[str, str]:
    """Pause a running crawl job."""
    logger.info("Pausing crawl job", job_id=job_id)

    try:
        success = await simweaver.pause_job(job_id)

        if not success:
            raise HTTPException(
                status_code=400,
                detail=f"Could not pause crawl job {job_id}"
            )

        return {"job_id": job_id, "message": f"Crawl job {job_id} paused successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to pause crawl job", job_id=job_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to pause crawl job: {str(e)}")


@router.post("/crawl/{job_id}/resume", response_model=Dict[str, str])
async def resume_crawl_job(job_id: str) -> Dict[str, str]:
    """Resume a paused crawl job."""
    logger.info("Resuming crawl job", job_id=job_id)

    try:
        success = await simweaver.resume_job(job_id)

        if not success:
            raise HTTPException(
                status_code=400,
                detail=f"Could not resume crawl job {job_id}"
            )

        return {"job_id": job_id, "message": f"Crawl job {job_id} resumed successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to resume crawl job", job_id=job_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to resume crawl job: {str(e)}")


@router.get("/crawl", response_model=Dict[str, Any])
async def list_crawl_jobs(
    status: Optional[str] = None,
    limit: int = 50,
    offset: int = 0
) -> Dict[str, Any]:
    """
    List crawl jobs with optional filtering.

    Returns a paginated list of crawl jobs, optionally filtered by status.
    """
    logger.debug(
        "Listing crawl jobs",
        status=status,
        limit=limit,
        offset=offset
    )

    try:
        all_jobs = simweaver.list_jobs()

        # Filter by status if provided
        if status:
            filtered_jobs = [job for job in all_jobs if job.status.value == status]
        else:
            filtered_jobs = all_jobs

        # Apply pagination
        total = len(filtered_jobs)
        paginated_jobs = filtered_jobs[offset:offset + limit]

        # Convert to summary format
        job_summaries = []
        for job in paginated_jobs:
            stats = CrawlStats(
                total_pages=job.stats.total_pages,
                pages_crawled=job.stats.pages_crawled,
                pages_pending=job.stats.pages_pending,
                pages_failed=job.stats.pages_failed,
                pages_per_second=job.stats.pages_per_second,
                avg_response_time=job.stats.avg_response_time,
                total_data_downloaded=job.stats.total_data_downloaded,
                unique_domains=job.stats.unique_domains,
                start_time=job.stats.start_time,
                end_time=job.stats.end_time,
                duration=job.stats.duration
            )

            summary = CrawlJobSummary(
                job_id=job.job_id,
                name=job.name,
                start_url=job.start_url,
                status=job.status.value,
                created_at=job.created_at,
                started_at=job.started_at,
                completed_at=job.completed_at,
                stats=stats
            )
            job_summaries.append(summary.model_dump())

        return {
            "jobs": job_summaries,
            "total": total,
            "limit": limit,
            "offset": offset,
            "has_more": offset + limit < total
        }

    except Exception as e:
        logger.error("Failed to list crawl jobs", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list crawl jobs: {str(e)}"
        )


@router.post("/crawl/demo", response_model=Dict[str, Any])
async def create_demo_jobs() -> Dict[str, Any]:
    """
    Create demo crawl jobs for testing the UI.

    Creates several sample jobs with different configurations and states.
    """
    logger.info("Creating demo crawl jobs")

    try:
        demo_jobs = []

        # Demo job 1: Fast crawl
        job_id_1 = simweaver.create_job(
            name="Fast Demo Crawl",
            start_url="https://example.com",
            config=SimWeaverConfig.get_preset("fast")
        )
        demo_jobs.append(job_id_1)

        # Demo job 2: Normal crawl (auto-start)
        job_id_2 = simweaver.create_job(
            name="Normal Demo Crawl",
            start_url="https://demo.net/products",
            config=SimWeaverConfig.get_preset("normal")
        )
        await simweaver.start_job(job_id_2)
        demo_jobs.append(job_id_2)

        # Demo job 3: Large crawl
        job_id_3 = simweaver.create_job(
            name="Large Site Crawl",
            start_url="https://testsite.org/blog",
            config=SimWeaverConfig.get_preset("large")
        )
        demo_jobs.append(job_id_3)

        # Demo job 4: Unreliable network simulation
        job_id_4 = simweaver.create_job(
            name="Unreliable Network Test",
            start_url="https://mockapi.dev/api",
            config=SimWeaverConfig.get_preset("unreliable")
        )
        demo_jobs.append(job_id_4)

        logger.info("Demo crawl jobs created", job_count=len(demo_jobs))

        return {
            "message": f"Created {len(demo_jobs)} demo crawl jobs",
            "job_ids": demo_jobs,
            "note": "One job has been auto-started to demonstrate running state"
        }

    except Exception as e:
        logger.error("Failed to create demo jobs", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create demo jobs: {str(e)}"
        )


@router.post("/crawl/logging/configure", response_model=Dict[str, Any])
async def configure_simweaver_logging(
    enable_performance: bool = True,
    enable_debug: bool = True,
    enable_trace: bool = False,
    enable_security: bool = True,
    enable_network: bool = True,
    enable_metrics: bool = True,
    slow_operation_threshold: float = 1.0,
    metrics_log_interval: int = 10
) -> Dict[str, Any]:
    """
    Configure SimWeaver logging levels and categories.

    This endpoint allows dynamic configuration of logging to test
    different aspects of the logging system.
    """
    logger.info(
        "Configuring SimWeaver logging",
        enable_performance=enable_performance,
        enable_debug=enable_debug,
        enable_trace=enable_trace,
        enable_security=enable_security,
        enable_network=enable_network,
        enable_metrics=enable_metrics
    )

    try:
        # Log the configuration change BEFORE switching
        old_categories = simweaver._get_enabled_categories()
        old_config = simweaver.logging_config.__dict__.copy()

        simweaver.enhanced_logger.state(
            "SimWeaver logging configuration changing",
            old_config_summary={
                "performance": simweaver.logging_config.enable_performance,
                "debug": simweaver.logging_config.enable_debug,
                "trace": simweaver.logging_config.enable_trace,
                "security": simweaver.logging_config.enable_security,
                "network": simweaver.logging_config.enable_network,
                "metrics": simweaver.logging_config.enable_metrics
            },
            old_categories=old_categories
        )

        # Also log to main logger
        logger.info(
            "SimWeaver logging configuration update initiated",
            old_categories=old_categories,
            new_settings={
                "enable_performance": enable_performance,
                "enable_debug": enable_debug,
                "enable_trace": enable_trace,
                "enable_security": enable_security,
                "enable_network": enable_network,
                "enable_metrics": enable_metrics
            }
        )

        # Create new logging configuration
        new_logging_config = SimWeaverLoggingConfig(
            enable_performance=enable_performance,
            enable_debug=enable_debug,
            enable_trace=enable_trace,
            enable_security=enable_security,
            enable_network=enable_network,
            enable_metrics=enable_metrics,
            slow_operation_threshold=slow_operation_threshold,
            metrics_log_interval=metrics_log_interval
        )

        # Update the global SimWeaver instance
        simweaver.logging_config = new_logging_config
        from spigaui.simweaver.logging_config import SimWeaverLogger
        simweaver.enhanced_logger = SimWeaverLogger(new_logging_config)

        # Log the configuration change completion (with new logger if enabled)
        new_categories = simweaver._get_enabled_categories()
        simweaver.enhanced_logger.state(
            "SimWeaver logging configuration updated successfully",
            new_config=new_logging_config.__dict__,
            enabled_categories=new_categories,
            config_change_completed=True
        )

        # Always log completion to main logger
        logger.info(
            "SimWeaver logging configuration updated",
            new_categories=new_categories,
            categories_changed=old_categories != new_categories,
            config_updated=True
        )

        return {
            "message": "SimWeaver logging configuration updated successfully",
            "config": {
                "enable_performance": enable_performance,
                "enable_debug": enable_debug,
                "enable_trace": enable_trace,
                "enable_security": enable_security,
                "enable_network": enable_network,
                "enable_metrics": enable_metrics,
                "slow_operation_threshold": slow_operation_threshold,
                "metrics_log_interval": metrics_log_interval
            },
            "enabled_categories": simweaver._get_enabled_categories()
        }

    except Exception as e:
        logger.error("Failed to configure SimWeaver logging", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to configure logging: {str(e)}"
        )


@router.get("/crawl/logging/files", response_model=Dict[str, Any])
async def list_log_files() -> Dict[str, Any]:
    """List available log files."""
    from pathlib import Path

    log_dir = Path("logs")
    if not log_dir.exists():
        return {"files": [], "message": "No log directory found"}

    files = []
    for log_file in log_dir.glob("*.log"):
        stat = log_file.stat()
        files.append({
            "name": log_file.name,
            "size": stat.st_size,
            "modified": stat.st_mtime,
            "path": str(log_file)
        })

    return {
        "files": sorted(files, key=lambda x: x["modified"], reverse=True),
        "message": f"Found {len(files)} log files"
    }


@router.get("/crawl/logging/files/{filename}", response_model=Dict[str, Any])
async def read_crawl_log_file(
    filename: str,
    lines: int = 100,
    offset: int = 0,
    search: str = None
) -> Dict[str, Any]:
    """
    Read content from a crawl log file.

    Args:
        filename: Name of the crawl log file to read
        lines: Number of lines to return (default: 100)
        offset: Number of lines to skip from the end (default: 0)
        search: Optional search term to filter lines
    """
    from pathlib import Path
    import json

    log_file = Path("logs") / filename

    # Security check - ensure file is in logs directory and is a .log file
    if not log_file.exists() or not log_file.suffix == ".log" or not str(log_file).startswith("logs"):
        raise HTTPException(status_code=404, detail="Crawl log file not found")

    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()

        # Apply search filter if provided
        if search:
            all_lines = [line for line in all_lines if search.lower() in line.lower()]

        # Get the requested slice
        total_lines = len(all_lines)
        start_idx = max(0, total_lines - lines - offset)
        end_idx = total_lines - offset if offset > 0 else total_lines
        selected_lines = all_lines[start_idx:end_idx]

        # Try to parse JSON lines, fall back to plain text
        parsed_lines = []
        for line_num, line in enumerate(selected_lines, start=start_idx + 1):
            line = line.strip()
            if not line:
                continue

            try:
                # Try to parse as JSON
                parsed = json.loads(line)
                parsed_lines.append({
                    "line_number": line_num,
                    "raw": line,
                    "parsed": parsed,
                    "is_json": True
                })
            except json.JSONDecodeError:
                # Plain text line
                parsed_lines.append({
                    "line_number": line_num,
                    "raw": line,
                    "parsed": None,
                    "is_json": False
                })

        return {
            "filename": filename,
            "total_lines": total_lines,
            "returned_lines": len(parsed_lines),
            "lines": parsed_lines,
            "search": search,
            "offset": offset
        }

    except Exception as e:
        logger.error("Failed to read crawl log file", filename=filename, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to read crawl log file: {str(e)}"
        )


@router.post("/crawl/logging/reset", response_model=Dict[str, str])
async def reset_crawl_logs() -> Dict[str, str]:
    """
    Reset (clear) all crawl log files.

    This will truncate all .log files in the logs directory, effectively
    clearing the crawl log history. Use with caution.
    """
    from pathlib import Path

    logger.warning("Resetting crawl logs - clearing all log files")

    try:
        log_dir = Path("logs")
        if not log_dir.exists():
            return {"message": "No log directory found - nothing to reset"}

        cleared_files = []
        for log_file in log_dir.glob("*.log"):
            try:
                # Truncate the file (clear contents but keep file)
                with open(log_file, 'w', encoding='utf-8') as f:
                    f.truncate(0)
                cleared_files.append(log_file.name)
                logger.info("Cleared crawl log file", filename=log_file.name)
            except Exception as e:
                logger.error("Failed to clear crawl log file", filename=log_file.name, error=str(e))

        if cleared_files:
            message = f"Successfully reset {len(cleared_files)} crawl log files: {', '.join(cleared_files)}"
        else:
            message = "No crawl log files found to reset"

        logger.info("Crawl log reset completed", cleared_files=cleared_files)
        return {"message": message}

    except Exception as e:
        logger.error("Failed to reset crawl logs", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to reset crawl logs: {str(e)}"
        )


@router.post("/crawl/logging/test", response_model=Dict[str, str])
async def test_simweaver_logging() -> Dict[str, str]:
    """
    Test all SimWeaver logging categories.

    Generates sample log messages in all categories to test the logging system.
    """
    logger.info("Testing SimWeaver logging categories")

    try:
        # Test all logging categories
        simweaver.enhanced_logger.performance(
            "Test performance log",
            operation="test_logging",
            duration_ms=123.45,
            memory_usage_mb=50.2
        )

        simweaver.enhanced_logger.error(
            "Test error log",
            error_type="test_error",
            error_code="TEST_001",
            is_simulated=True
        )

        simweaver.enhanced_logger.debug(
            "Test debug log",
            component="logging_test",
            debug_level="verbose",
            test_data={"key": "value"}
        )

        simweaver.enhanced_logger.trace(
            "Test trace log",
            trace_id="test_trace_001",
            execution_path="test -> logging -> trace",
            variables={"x": 1, "y": 2}
        )

        simweaver.enhanced_logger.security(
            "Test security log",
            event_type="test_security_event",
            threat_level="low",
            source="logging_test"
        )

        simweaver.enhanced_logger.network(
            "Test network log",
            url="https://test.example.com",
            method="GET",
            status_code=200,
            response_time_ms=150
        )

        simweaver.enhanced_logger.state(
            "Test state log",
            entity="test_entity",
            old_state="initial",
            new_state="tested"
        )

        simweaver.enhanced_logger.metrics(
            "Test metrics log",
            metric_name="test_metric",
            value=42.0,
            unit="requests/second"
        )

        return {
            "message": "All SimWeaver logging categories tested successfully",
            "categories_tested": [
                "performance", "error", "debug", "trace",
                "security", "network", "state", "metrics"
            ]
        }

    except Exception as e:
        logger.error("Failed to test SimWeaver logging", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to test logging: {str(e)}"
        )


@router.post("/crawl/logging/preset/{preset_name}", response_model=Dict[str, Any])
async def set_logging_preset(preset_name: str) -> Dict[str, Any]:
    """
    Set SimWeaver logging to a predefined preset.

    Available presets:
    - minimal: Only errors and basic info
    - performance: Performance timing and metrics only
    - error_testing: Comprehensive error logging
    - network: Network request/response focus
    - state_tracking: State transitions and discovery
    - debug: Comprehensive debugging without trace
    - full_trace: Everything enabled (very verbose)
    - production: Balanced production-like logging
    """
    logger.info("Setting SimWeaver logging preset", preset=preset_name)

    try:
        # Get the preset configuration
        new_config = SimWeaverLoggingConfig.get_preset(preset_name)

        # Log the preset change BEFORE switching (always visible)
        old_categories = simweaver._get_enabled_categories()
        old_config = {
            "preset": "current",
            "categories": old_categories,
            "config": simweaver.logging_config.__dict__
        }
        new_config_dict = {
            "preset": preset_name,
            "categories": [cat.value for cat in LogCategory if new_config.is_enabled(cat)],
            "config": new_config.__dict__
        }

        # Use special config change logging that's always visible
        simweaver.enhanced_logger.log_config_change(
            "preset_switch",
            old_config,
            new_config_dict
        )

        # Also log to main API logger
        logger.info(
            "SimWeaver logging preset change initiated",
            preset=preset_name,
            old_categories=old_categories
        )

        # Update the global SimWeaver instance
        simweaver.logging_config = new_config
        from spigaui.simweaver.logging_config import SimWeaverLogger
        simweaver.enhanced_logger = SimWeaverLogger(new_config)

        # Log the preset activation with NEW logger (if state logging is enabled)
        new_categories = simweaver._get_enabled_categories()
        simweaver.enhanced_logger.state(
            f"SimWeaver logging preset '{preset_name}' activated successfully",
            preset=preset_name,
            enabled_categories=new_categories,
            config_change_completed=True
        )

        # Always log completion to main logger
        logger.info(
            "SimWeaver logging preset activated",
            preset=preset_name,
            new_categories=new_categories,
            categories_changed=old_categories != new_categories
        )

        return {
            "message": f"SimWeaver logging preset '{preset_name}' activated successfully",
            "preset": preset_name,
            "enabled_categories": simweaver._get_enabled_categories(),
            "config_summary": {
                "performance": new_config.enable_performance,
                "error": new_config.enable_error,
                "debug": new_config.enable_debug,
                "trace": new_config.enable_trace,
                "security": new_config.enable_security,
                "network": new_config.enable_network,
                "state": new_config.enable_state,
                "metrics": new_config.enable_metrics
            }
        }

    except ValueError as e:
        available_presets = SimWeaverLoggingConfig.list_presets()
        raise HTTPException(
            status_code=400,
            detail=f"Invalid preset: {str(e)}. Available presets: {available_presets}"
        )
    except Exception as e:
        logger.error("Failed to set logging preset", preset=preset_name, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to set logging preset: {str(e)}"
        )


@router.get("/crawl/logging/presets", response_model=Dict[str, Any])
async def list_logging_presets() -> Dict[str, Any]:
    """List all available logging presets with descriptions."""
    try:
        presets = SimWeaverLoggingConfig.list_presets()

        return {
            "message": "Available SimWeaver logging presets",
            "presets": presets,
            "usage": "Use POST /api/crawl/logging/preset/{preset_name} to activate a preset"
        }

    except Exception as e:
        logger.error("Failed to list logging presets", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list presets: {str(e)}"
        )


@router.post("/crawl/emergency/stop-all", response_model=Dict[str, Any])
async def emergency_stop_all_jobs() -> Dict[str, Any]:
    """
    EMERGENCY STOP - Immediately stop all running SimWeaver jobs.

    This is a safety endpoint to quickly stop all crawling activity
    if SimWeaver is running amok or consuming too many resources.
    """
    logger.warning("EMERGENCY STOP requested - stopping all SimWeaver jobs")

    try:
        # Get all active jobs
        active_jobs = list(simweaver.active_jobs)
        stopped_jobs = []
        failed_to_stop = []

        # Stop each active job
        for job_id in active_jobs:
            try:
                success = await simweaver.stop_job(job_id)
                if success:
                    stopped_jobs.append(job_id)
                else:
                    failed_to_stop.append(job_id)
            except Exception as e:
                logger.error(f"Failed to stop job {job_id}", error=str(e))
                failed_to_stop.append(job_id)

        # Also clear the active jobs set as a safety measure
        simweaver.active_jobs.clear()

        # Log the emergency stop
        logger.warning(
            "Emergency stop completed",
            stopped_jobs=len(stopped_jobs),
            failed_to_stop=len(failed_to_stop),
            total_active_before=len(active_jobs)
        )

        return {
            "message": "Emergency stop completed",
            "stopped_jobs": stopped_jobs,
            "failed_to_stop": failed_to_stop,
            "total_stopped": len(stopped_jobs),
            "total_failed": len(failed_to_stop),
            "active_jobs_remaining": len(simweaver.active_jobs),
            "emergency_stop": True
        }

    except Exception as e:
        logger.error("Emergency stop failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Emergency stop failed: {str(e)}"
        )


@router.post("/crawl/emergency/kill-engine", response_model=Dict[str, str])
async def emergency_kill_engine() -> Dict[str, str]:
    """
    NUCLEAR OPTION - Reset the entire SimWeaver engine.

    This completely resets SimWeaver by clearing all jobs and state.
    Use only if stop-all doesn't work or if the engine is corrupted.
    """
    logger.critical("NUCLEAR OPTION - Resetting entire SimWeaver engine")

    try:
        # Clear all jobs and state
        simweaver.jobs.clear()
        simweaver.active_jobs.clear()
        simweaver.operation_timers.clear()

        # Log the nuclear reset
        logger.critical(
            "SimWeaver engine reset completed",
            all_jobs_cleared=True,
            all_timers_cleared=True,
            engine_reset=True
        )

        return {
            "message": "SimWeaver engine completely reset",
            "warning": "All job data has been cleared",
            "nuclear_option": "true"
        }

    except Exception as e:
        logger.critical("Nuclear reset failed", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Nuclear reset failed: {str(e)}"
        )


@router.get("/crawl/status/engine", response_model=Dict[str, Any])
async def get_engine_status() -> Dict[str, Any]:
    """
    Get current SimWeaver engine status for monitoring.

    Useful for checking if the engine is healthy or if emergency action is needed.
    """
    try:
        total_jobs = len(simweaver.jobs)
        active_jobs = len(simweaver.active_jobs)
        active_timers = len(simweaver.operation_timers)

        # Check for potential issues
        warnings = []
        if active_jobs > 10:
            warnings.append("High number of active jobs")
        if active_timers > 50:
            warnings.append("High number of active operation timers")
        if total_jobs > 1000:
            warnings.append("Very high total job count")

        # Get job status breakdown
        status_counts = {}
        for job in simweaver.jobs.values():
            status = job.status.value
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            "engine_healthy": len(warnings) == 0,
            "total_jobs": total_jobs,
            "active_jobs": active_jobs,
            "active_timers": active_timers,
            "job_status_breakdown": status_counts,
            "warnings": warnings,
            "emergency_endpoints": {
                "stop_all": "/api/crawl/emergency/stop-all",
                "kill_engine": "/api/crawl/emergency/kill-engine"
            }
        }

    except Exception as e:
        logger.error("Failed to get engine status", error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get engine status: {str(e)}"
        )
