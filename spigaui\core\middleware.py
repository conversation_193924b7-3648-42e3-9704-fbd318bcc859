"""
Middleware for SpigaUI FastAPI application.

Includes request logging, performance monitoring, and error handling.
"""

import time
import uuid
from typing import Callable

import structlog
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from spigaui.core.config import get_settings
from spigaui.core.logging import log_error, log_performance, log_request, log_response


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests and responses."""

    def __init__(self, app, logger_name: str = "spigaui.http"):
        super().__init__(app)
        self.logger = structlog.get_logger(logger_name)
        self.settings = get_settings()

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and response with logging."""
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Add request ID to context
        with structlog.contextvars.bound_contextvars(request_id=request_id):
            # Skip logging for health checks and static files if configured
            skip_paths = {"/health", "/api/health", "/favicon.ico"}
            should_log = (
                self.settings.log_requests 
                and request.url.path not in skip_paths
                and not request.url.path.startswith("/static")
            )
            
            # Record start time
            start_time = time.time()
            
            # Log incoming request
            if should_log:
                log_request(
                    request_id=request_id,
                    method=request.method,
                    path=request.url.path,
                    query_params=str(request.query_params) if request.query_params else None,
                    user_agent=request.headers.get("user-agent"),
                    client_ip=self._get_client_ip(request),
                )
            
            try:
                # Process request
                response = await call_next(request)
                
                # Calculate duration
                duration_ms = (time.time() - start_time) * 1000
                
                # Log response
                if should_log:
                    log_response(
                        request_id=request_id,
                        status_code=response.status_code,
                        duration_ms=duration_ms,
                    )
                
                # Log performance metrics for slow requests
                if duration_ms > 1000:  # Log requests taking more than 1 second
                    log_performance(
                        operation=f"{request.method} {request.url.path}",
                        duration_ms=duration_ms,
                        request_id=request_id,
                        status_code=response.status_code,
                    )
                
                # Add request ID to response headers for debugging
                response.headers["X-Request-ID"] = request_id
                
                return response
                
            except Exception as exc:
                # Calculate duration even for errors
                duration_ms = (time.time() - start_time) * 1000
                
                # Log error
                log_error(
                    exc,
                    context="HTTP request processing",
                    request_id=request_id,
                    method=request.method,
                    path=request.url.path,
                    duration_ms=duration_ms,
                )
                
                # Re-raise the exception to let FastAPI handle it
                raise

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers (when behind a proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if request.client:
            return request.client.host
        
        return "unknown"


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """Middleware for centralized error handling and logging."""

    def __init__(self, app, logger_name: str = "spigaui.errors"):
        super().__init__(app)
        self.logger = structlog.get_logger(logger_name)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with error handling."""
        try:
            return await call_next(request)
        except Exception as exc:
            # Log the error with context
            self.logger.error(
                "Unhandled exception in request processing",
                error_type=type(exc).__name__,
                error_message=str(exc),
                method=request.method,
                path=request.url.path,
                exc_info=True,
            )
            
            # Re-raise to let FastAPI's exception handlers deal with it
            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add CSP header for API endpoints
        if request.url.path.startswith("/api"):
            response.headers["Content-Security-Policy"] = "default-src 'none'"
        
        return response
