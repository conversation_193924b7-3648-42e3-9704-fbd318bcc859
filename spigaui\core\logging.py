"""
Structured logging configuration for SpigaUI.

Uses structlog for consistent, structured logging across the application.
Supports both console and file output with rotation.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Any, Dict, List

import structlog
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from spigaui.core.config import get_settings


def _setup_file_logging(settings) -> List[logging.Handler]:
    """Set up file logging with rotation."""
    handlers = []

    if settings.log_to_file:
        # Create logs directory if it doesn't exist
        log_dir = Path(settings.log_dir)
        log_dir.mkdir(exist_ok=True)

        # Main application log file
        app_log_file = log_dir / "spigaui.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=settings.log_max_size_mb * 1024 * 1024,  # Convert MB to bytes
            backupCount=settings.log_backup_count,
            encoding="utf-8",
        )

        # Error log file (errors and above only)
        error_log_file = log_dir / "spigaui_errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=settings.log_max_size_mb * 1024 * 1024,
            backupCount=settings.log_backup_count,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.ERROR)

        # Set formatters based on configuration
        if settings.log_file_format.lower() == "json":
            formatter = logging.Formatter('%(message)s')
        else:
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )

        app_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)

        handlers.extend([app_handler, error_handler])

    return handlers


def setup_logging() -> None:
    """Configure structured logging for the application."""
    settings = get_settings()

    # Prepare handlers
    handlers = []

    # Console handler with Rich formatting
    console_handler = RichHandler(
        console=Console(stderr=False),
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True,
    )
    handlers.append(console_handler)

    # File handlers (if enabled)
    file_handlers = _setup_file_logging(settings)
    handlers.extend(file_handlers)

    # Configure standard library logging with our handlers
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))

    # Clear any existing handlers to avoid duplicates
    root_logger.handlers.clear()

    # Add our handlers to the root logger
    for handler in handlers:
        root_logger.addHandler(handler)

    # Configure structlog processors
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
        structlog.processors.TimeStamper(fmt="ISO"),
    ]

    # Use JSON renderer for structured output
    processors.append(structlog.processors.JSONRenderer())

    # Configure structlog to use standard library logging
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, settings.log_level.upper())
        ),
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )

    # Test that logging is working by writing a startup message
    test_logger = structlog.get_logger("spigaui.startup")
    test_logger.info(
        "Logging system initialized",
        log_level=settings.log_level,
        debug_mode=settings.debug,
        file_logging_enabled=settings.enable_file_logging,
        handlers_configured=len(handlers)
    )


def get_logger(name: str) -> structlog.BoundLogger:
    """Get a structured logger instance."""
    return structlog.get_logger(name)


def log_request(request_id: str, method: str, path: str, **kwargs: Any) -> None:
    """Log an HTTP request."""
    logger = get_logger("spigaui.request")
    logger.info(
        "HTTP request",
        request_id=request_id,
        method=method,
        path=path,
        **kwargs,
    )


def log_response(request_id: str, status_code: int, duration_ms: float, **kwargs: Any) -> None:
    """Log an HTTP response."""
    logger = get_logger("spigaui.response")
    logger.info(
        "HTTP response",
        request_id=request_id,
        status_code=status_code,
        duration_ms=duration_ms,
        **kwargs,
    )


def log_job_event(job_id: str, event_type: str, **kwargs: Any) -> None:
    """Log a job-related event."""
    logger = get_logger("spigaui.job")
    logger.info(
        "Job event",
        job_id=job_id,
        event_type=event_type,
        **kwargs,
    )


def log_error(error: Exception, context: str = "", **kwargs: Any) -> None:
    """Log an error with context."""
    logger = get_logger("spigaui.error")
    logger.error(
        "Application error",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context,
        **kwargs,
        exc_info=True,
    )


def log_performance(operation: str, duration_ms: float, **kwargs: Any) -> None:
    """Log performance metrics."""
    logger = get_logger("spigaui.performance")
    logger.info(
        "Performance metric",
        operation=operation,
        duration_ms=duration_ms,
        **kwargs,
    )


def log_security_event(event_type: str, user_id: str = None, **kwargs: Any) -> None:
    """Log security-related events."""
    logger = get_logger("spigaui.security")
    logger.warning(
        "Security event",
        event_type=event_type,
        user_id=user_id,
        **kwargs,
    )


def configure_uvicorn_logging() -> None:
    """Configure uvicorn logging to use our structured logging."""
    # Disable uvicorn's default logging
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_access_logger = logging.getLogger("uvicorn.access")

    # Set levels but let our handlers manage the output
    uvicorn_logger.setLevel(logging.INFO)
    uvicorn_access_logger.setLevel(logging.INFO)

    # Remove default handlers to avoid duplicate logs
    uvicorn_logger.handlers.clear()
    uvicorn_access_logger.handlers.clear()

    # Let our root logger handle uvicorn logs
    uvicorn_logger.propagate = True
    uvicorn_access_logger.propagate = True
