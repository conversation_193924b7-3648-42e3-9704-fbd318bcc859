import React, { useState, useEffect, useRef } from 'react'
import { loggingApi, LogEntry } from '../lib/api'

interface LogViewerProps {
  height?: string
  maxLogs?: number
  autoStart?: boolean
  showControls?: boolean
  filterCategory?: string
  filterLevel?: string
}

const LogViewer: React.FC<LogViewerProps> = ({
  height = 'h-64',
  maxLogs = 100,
  autoStart = false,
  showControls = true,
  filterCategory,
  filterLevel
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [isStreaming, setIsStreaming] = useState(autoStart)
  const [selectedCategory, setSelectedCategory] = useState(filterCategory || 'all')
  const [selectedLevel, setSelectedLevel] = useState(filterLevel || 'all')
  const [isAutoScroll, setIsAutoScroll] = useState(true)
  const logsEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive (only if user hasn't scrolled up)
  useEffect(() => {
    if (isAutoScroll && logsEndRef.current) {
      // Only auto-scroll if the user is near the bottom (within 100px)
      const container = logsEndRef.current.parentElement
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        if (isNearBottom) {
          logsEndRef.current.scrollIntoView({ behavior: 'smooth' })
        }
      }
    }
  }, [logs, isAutoScroll])

  // Log streaming effect
  useEffect(() => {
    if (!isStreaming) return

    const fetchLogs = async () => {
      try {
        const recentLogs = await loggingApi.getRecentLogs(20)
        setLogs(prev => {
          const existingTimestamps = new Set(prev.map(log => log.timestamp))
          const newLogs = recentLogs.filter(log => !existingTimestamps.has(log.timestamp))
          return [...prev, ...newLogs].slice(-maxLogs)
        })
      } catch (error) {
        console.error('Error fetching logs:', error)
      }
    }

    fetchLogs()
    const interval = setInterval(fetchLogs, 2000)
    return () => clearInterval(interval)
  }, [isStreaming, maxLogs])

  // Filter logs
  const filteredLogs = logs.filter(log => {
    const categoryMatch = selectedCategory === 'all' || log.category === selectedCategory
    const levelMatch = selectedLevel === 'all' || log.level === selectedLevel
    return categoryMatch && levelMatch
  })

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-400'
      case 'warning': return 'text-yellow-400'
      case 'info': return 'text-blue-400'
      case 'debug': return 'text-gray-400'
      default: return 'text-white'
    }
  }

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case 'performance': return 'bg-blue-600'
      case 'error': return 'bg-red-600'
      case 'debug': return 'bg-gray-600'
      case 'security': return 'bg-purple-600'
      case 'network': return 'bg-green-600'
      case 'state': return 'bg-yellow-600'
      case 'metrics': return 'bg-indigo-600'
      default: return 'bg-gray-500'
    }
  }

  const handleToggleStreaming = () => {
    setIsStreaming(!isStreaming)
  }

  const handleClearLogs = () => {
    setLogs([])
  }

  const handleScrollToBottom = () => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden">
      {showControls && (
        <div className="sticky top-0 z-10 px-4 py-3 border-b border-gray-700 bg-gray-800">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-medium text-white">Live Logs</h3>
              {isStreaming && (
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                  <span className="text-green-400 text-sm">Streaming</span>
                </div>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              {/* Category Filter */}
              <select
                className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                <option value="performance">Performance</option>
                <option value="error">Error</option>
                <option value="debug">Debug</option>
                <option value="security">Security</option>
                <option value="network">Network</option>
                <option value="state">State</option>
                <option value="metrics">Metrics</option>
              </select>

              {/* Level Filter */}
              <select
                className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-white text-sm"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="all">All Levels</option>
                <option value="error">Error</option>
                <option value="warning">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>

              {/* Auto-scroll */}
              <label className="flex items-center text-sm text-white">
                <input
                  type="checkbox"
                  checked={isAutoScroll}
                  onChange={(e) => setIsAutoScroll(e.target.checked)}
                  className="mr-1"
                />
                Auto-scroll
              </label>

              {/* Controls */}
              <button
                onClick={handleToggleStreaming}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  isStreaming
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-green-600 hover:bg-green-700 text-white'
                }`}
              >
                {isStreaming ? 'Stop' : 'Start'}
              </button>
              
              <button
                onClick={handleClearLogs}
                className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}
      
      <div className={`${height} overflow-y-auto bg-gray-900 font-mono text-sm`}>
        {filteredLogs.length > 0 ? (
          <div className="p-3 space-y-1">
            {filteredLogs.map((log, index) => (
              <div key={index} className="flex items-start space-x-2 py-1 hover:bg-gray-800 rounded px-2">
                <span className="text-gray-500 text-xs whitespace-nowrap">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </span>
                <span className={`text-xs font-medium ${getLevelColor(log.level)} uppercase min-w-[3rem]`}>
                  {log.level}
                </span>
                {log.category && (
                  <span className={`text-xs px-1 py-0.5 rounded ${getCategoryColor(log.category)} text-white min-w-[4rem] text-center`}>
                    {log.category}
                  </span>
                )}
                <span className="text-white flex-1 text-xs">
                  <span className="font-medium">{log.event}</span>
                  {log.message && <span className="text-gray-300"> - {log.message}</span>}
                  {log.job_id && <span className="text-gray-500"> [{log.job_id.slice(0, 8)}]</span>}
                </span>
              </div>
            ))}
            <div ref={logsEndRef} />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <p className="mb-2">No logs to display</p>
              <p className="text-sm">
                {isStreaming ? 'Waiting for log entries...' : 'Click "Start" to begin monitoring logs'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default LogViewer
