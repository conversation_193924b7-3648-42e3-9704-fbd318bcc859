"""
Job monitoring and real-time updates for SpigaUI.

Provides Server-Sent Events (SSE) for real-time job progress updates.
"""

from typing import AsyncGenerator, Dict, Any
import asyncio
import json

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
import structlog

from spigaui.services.job_monitor import JobMonitor
from spigaui.core.logging import log_job_event

router = APIRouter()
logger = structlog.get_logger(__name__)


@router.get("/jobs/{job_id}/events")
async def stream_job_events(job_id: str) -> StreamingResponse:
    """
    Stream real-time job events via Server-Sent Events (SSE).
    
    Provides a continuous stream of job progress updates, status changes,
    and results as they become available.
    """
    logger.info("Starting job event stream", job_id=job_id)
    
    async def event_generator() -> AsyncGenerator[str, None]:
        """Generate Server-Sent Events for job updates."""
        job_monitor = JobMonitor()
        
        try:
            # Send initial connection event
            yield f"data: {json.dumps({'type': 'connected', 'job_id': job_id})}\n\n"
            
            # Stream job events
            async for event in job_monitor.stream_job_events(job_id):
                event_data = {
                    "type": event.type,
                    "job_id": job_id,
                    "timestamp": event.timestamp.isoformat(),
                    "data": event.data
                }
                
                yield f"data: {json.dumps(event_data)}\n\n"
                
                # Break if job is completed or failed
                if event.type in ["completed", "failed", "cancelled"]:
                    logger.info("Job event stream ending", job_id=job_id, reason=event.type)
                    break
                    
        except asyncio.CancelledError:
            logger.info("Job event stream cancelled", job_id=job_id)
            yield f"data: {json.dumps({'type': 'disconnected', 'job_id': job_id})}\n\n"
        except Exception as e:
            logger.error("Error in job event stream", job_id=job_id, error=str(e))
            error_event = {
                "type": "error",
                "job_id": job_id,
                "error": str(e)
            }
            yield f"data: {json.dumps(error_event)}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        }
    )


@router.get("/jobs/{job_id}/logs")
async def get_job_logs(
    job_id: str,
    limit: int = 100,
    offset: int = 0
) -> Dict[str, Any]:
    """
    Get job execution logs.
    
    Returns paginated logs for a specific job.
    """
    logger.debug("Getting job logs", job_id=job_id, limit=limit, offset=offset)
    
    try:
        job_monitor = JobMonitor()
        logs = await job_monitor.get_job_logs(job_id, limit=limit, offset=offset)
        
        return {
            "job_id": job_id,
            "logs": logs,
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error("Failed to get job logs", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job logs: {str(e)}"
        )


@router.get("/jobs/{job_id}/results")
async def get_job_results(
    job_id: str,
    format: str = "json"
) -> Dict[str, Any]:
    """
    Get job results.
    
    Returns the results of a completed crawl job in the specified format.
    """
    logger.debug("Getting job results", job_id=job_id, format=format)
    
    try:
        job_monitor = JobMonitor()
        results = await job_monitor.get_job_results(job_id, format=format)
        
        if not results:
            raise HTTPException(
                status_code=404,
                detail=f"No results found for job {job_id}"
            )
        
        return {
            "job_id": job_id,
            "format": format,
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get job results", job_id=job_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job results: {str(e)}"
        )
