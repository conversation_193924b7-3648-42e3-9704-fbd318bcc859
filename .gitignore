# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/*.log
logs/*.log.*
logs/spigaui*.log
logs/spigaui*.log.*

# Database
*.db
*.sqlite
*.sqlite3

# Node.js (Frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Frontend build
frontend/dist/
frontend/build/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Celery
celerybeat-schedule
celerybeat.pid

# Redis
dump.rdb

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
.cache/
