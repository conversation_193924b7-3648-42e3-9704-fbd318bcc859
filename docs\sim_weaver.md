# SimWeaver - Comprehensive Crawler Simulator

**SimWeaver** is a sophisticated web crawler simulator designed specifically for SpigaUI development and testing. It provides realistic crawling behavior with comprehensive logging capabilities, making it the perfect testbed for UI development and logging system validation.

---

## 🎯 **Purpose & Benefits**

### **Why SimWeaver?**
- **Independent UI Development**: Build and test the frontend without needing the actual SpigaMonde crawler
- **Logging System Testing**: Comprehensive testbed for validating our structured logging infrastructure
- **Error Scenario Testing**: Simulate various failure modes and edge cases
- **Performance Testing**: Test UI responsiveness under different crawl speeds and loads
- **Real-time Features**: Validate SSE (Server-Sent Events) and progress tracking
- **Debugging**: Detailed trace logging for troubleshooting crawl flow

### **Key Advantages**
- **Predictable Behavior**: Controlled, reproducible crawl scenarios
- **Configurable Parameters**: Adjust speed, failure rates, and complexity
- **Rich Logging**: 8 different logging categories with granular control
- **Realistic Simulation**: Authentic URLs, timing, and error patterns
- **Multiple Job States**: Complete job lifecycle simulation

---

## 🏗️ **Architecture Overview**

### **Modular Structure**
```
spigaui/simweaver/
├── __init__.py           # Module exports and global instance
├── engine.py             # Core simulation engine
├── models.py             # Data models and enums
├── config.py             # Crawler configuration
├── data_generator.py     # Realistic data generation
└── logging_config.py     # Comprehensive logging system
```

### **Core Components**

#### **1. SimWeaver Engine** (`engine.py`)
- Job lifecycle management (create, start, pause, resume, stop)
- Crawl simulation orchestration
- Performance monitoring and timing
- Error handling and recovery

#### **2. Data Models** (`models.py`)
- `CrawlJob`: Complete job representation
- `CrawlPage`: Individual page data
- `CrawlStats`: Performance metrics
- `CrawlStatus` & `PageStatus`: State enumerations

#### **3. Data Generator** (`data_generator.py`)
- Realistic URL generation
- Content size simulation
- Error scenario creation
- Timing variation

#### **4. Configuration** (`config.py`)
- Crawl parameters (depth, pages, timing)
- Failure simulation rates
- Performance characteristics
- Preset configurations

#### **5. Logging System** (`logging_config.py`)
- 8 logging categories
- Configurable verbosity levels
- Performance thresholds
- Preset configurations

---

## 📊 **Logging Categories & Strategy**

### **8 Main Logging Categories**

| Category | Purpose | Verbosity | Use Case |
|----------|---------|-----------|----------|
| **PERFORMANCE** | Operation timing, slow operations, efficiency metrics | Medium | Performance testing, optimization |
| **ERROR** | All types of errors and failures | Low | Error handling validation |
| **DEBUG** | Development and troubleshooting information | Medium | General debugging |
| **TRACE** | Very detailed execution flow | Very High | Deep debugging |
| **SECURITY** | Security events and access monitoring | Low | Security testing |
| **NETWORK** | Request/response simulation details | High | Network behavior testing |
| **STATE** | Entity state transitions | Medium | State management debugging |
| **METRICS** | Statistics and measurements | Low | Performance monitoring |

### **Granular Sub-Categories**

#### **Error Logging**
- `log_all_errors`: General error logging
- `log_simulated_errors`: Intentional test errors
- `log_network_errors`: Network-related failures

#### **Debug Logging**
- `log_page_discovery`: New page detection
- `log_url_generation`: URL creation process
- `log_state_transitions`: State change tracking

#### **Trace Logging** (Very Verbose)
- `trace_crawl_flow`: Detailed crawl execution
- `trace_data_generation`: Data creation process
- `trace_statistics_updates`: Metrics calculation

#### **Network Logging**
- `log_request_details`: HTTP request simulation
- `log_response_details`: HTTP response simulation
- `log_connection_issues`: Connection problems

---

## 🎛️ **Logging Presets for Different Testing Scenarios**

### **Recommended Testing Strategy**

#### **1. Minimal** (`minimal`)
```json
{
  "enable_performance": false,
  "enable_error": true,
  "enable_debug": false,
  "enable_trace": false,
  "enable_security": false,
  "enable_network": false,
  "enable_state": false,
  "enable_metrics": false
}
```
**Use Case**: Basic error testing, least verbose output
**Best For**: Initial development, basic functionality testing

#### **2. Performance** (`performance`)
```json
{
  "enable_performance": true,
  "enable_error": true,
  "enable_metrics": true,
  "slow_operation_threshold": 0.5,
  "metrics_log_interval": 5
}
```
**Use Case**: Performance optimization, timing analysis
**Best For**: Speed testing, bottleneck identification

#### **3. Error Testing** (`error_testing`)
```json
{
  "enable_error": true,
  "enable_debug": true,
  "enable_security": true,
  "enable_network": true,
  "enable_state": true,
  "log_all_errors": true,
  "log_simulated_errors": true,
  "security_event_rate": 0.1
}
```
**Use Case**: Comprehensive error scenario testing
**Best For**: Error handling validation, edge case testing

#### **4. Network** (`network`)
```json
{
  "enable_network": true,
  "enable_error": true,
  "log_request_details": true,
  "log_response_details": true,
  "log_connection_issues": true
}
```
**Use Case**: Network behavior simulation testing
**Best For**: Request/response flow validation

#### **5. State Tracking** (`state_tracking`)
```json
{
  "enable_state": true,
  "enable_debug": true,
  "enable_error": true,
  "log_page_discovery": true,
  "log_state_transitions": true
}
```
**Use Case**: State management debugging
**Best For**: Job lifecycle testing, state transition validation

#### **6. Debug** (`debug`)
```json
{
  "enable_performance": true,
  "enable_error": true,
  "enable_debug": true,
  "enable_security": true,
  "enable_network": true,
  "enable_state": true,
  "enable_metrics": true,
  "enable_trace": false
}
```
**Use Case**: Comprehensive debugging without overwhelming trace logs
**Best For**: General development, troubleshooting

#### **7. Full Trace** (`full_trace`)
```json
{
  "enable_performance": true,
  "enable_error": true,
  "enable_debug": true,
  "enable_trace": true,
  "enable_security": true,
  "enable_network": true,
  "enable_state": true,
  "enable_metrics": true,
  "trace_crawl_flow": true,
  "trace_data_generation": true,
  "metrics_log_interval": 1
}
```
**Use Case**: Deep debugging, complete execution tracing
**Best For**: Complex issue investigation (very verbose)

#### **8. Production** (`production`)
```json
{
  "enable_performance": true,
  "enable_error": true,
  "enable_security": true,
  "enable_metrics": true,
  "slow_operation_threshold": 2.0,
  "metrics_log_interval": 20,
  "simulate_security_events": false
}
```
**Use Case**: Production-like logging simulation
**Best For**: Production readiness testing

---

## 🚀 **API Endpoints**

### **Job Management**

#### **Create Demo Jobs**
```http
POST /api/crawl/demo
```
Creates multiple demo jobs with different configurations for testing.

**Response:**
```json
{
  "message": "Created 4 demo crawl jobs",
  "job_ids": ["uuid1", "uuid2", "uuid3", "uuid4"],
  "note": "One job has been auto-started to demonstrate running state"
}
```

#### **Create Custom Job**
```http
POST /api/crawl
Content-Type: application/json

{
  "name": "Test Crawl",
  "start_url": "https://example.com",
  "config": {
    "max_pages": 50,
    "max_depth": 2,
    "delay_min": 0.1,
    "delay_max": 1.0,
    "failure_rate": 0.05
  }
}
```

#### **Start Job**
```http
POST /api/crawl/{job_id}/start
```

#### **Control Job**
```http
POST /api/crawl/{job_id}/pause
POST /api/crawl/{job_id}/resume
POST /api/crawl/{job_id}/stop
```

#### **Get Job Details**
```http
GET /api/crawl/{job_id}
```

#### **List All Jobs**
```http
GET /api/crawl?status=running&limit=10&offset=0
```

### **Emergency Safety Endpoints** 🚨

#### **Emergency Stop All Jobs**
```http
POST /api/crawl/emergency/stop-all
```
**Purpose**: Immediately stop all running SimWeaver jobs if the system is running amok.

**Response:**
```json
{
  "message": "Emergency stop completed",
  "stopped_jobs": ["job1", "job2"],
  "failed_to_stop": [],
  "total_stopped": 2,
  "total_failed": 0,
  "active_jobs_remaining": 0,
  "emergency_stop": true
}
```

#### **Nuclear Reset Engine**
```http
POST /api/crawl/emergency/kill-engine
```
**Purpose**: Complete engine reset - clears ALL jobs and state. Use only if stop-all fails.

**⚠️ WARNING**: This destroys all job data!

#### **Engine Health Check**
```http
GET /api/crawl/status/engine
```
**Purpose**: Monitor engine health and detect potential issues.

**Response:**
```json
{
  "engine_healthy": true,
  "total_jobs": 5,
  "active_jobs": 2,
  "active_timers": 3,
  "job_status_breakdown": {
    "running": 2,
    "completed": 2,
    "queued": 1
  },
  "warnings": [],
  "emergency_endpoints": {
    "stop_all": "/api/crawl/emergency/stop-all",
    "kill_engine": "/api/crawl/emergency/kill-engine"
  }
}
```

### **Logging Configuration**

#### **List Available Presets**
```http
GET /api/crawl/logging/presets
```

**Response:**
```json
{
  "message": "Available SimWeaver logging presets",
  "presets": {
    "minimal": "Only errors and basic info - least verbose",
    "performance": "Performance timing and metrics only",
    "error_testing": "Comprehensive error and security logging",
    "network": "Network request/response simulation focus",
    "state_tracking": "State transitions and page discovery",
    "debug": "Comprehensive debugging without trace",
    "full_trace": "Everything enabled - most verbose",
    "production": "Balanced logging for production simulation"
  }
}
```

#### **Set Logging Preset**
```http
POST /api/crawl/logging/preset/{preset_name}
```

**Example:**
```http
POST /api/crawl/logging/preset/debug
```

**Logging Switch Behavior:**
- Configuration changes are **always logged** regardless of current settings
- Uses both SimWeaver enhanced logger AND main API logger
- Logs BEFORE switching (with old logger) and AFTER switching (with new logger)
- Ensures visibility even when switching to `minimal` preset

**Example Switch Log Output:**
```json
{
  "timestamp": "2025-08-28T18:45:10.123456Z",
  "level": "info",
  "message": "SimWeaver configuration preset_switch",
  "change_type": "preset_switch",
  "old_config": {
    "preset": "current",
    "categories": ["performance", "error", "debug", "state"],
    "config": {...}
  },
  "new_config": {
    "preset": "minimal",
    "categories": ["error"],
    "config": {...}
  },
  "always_visible": true
}
```

#### **Custom Logging Configuration**
```http
POST /api/crawl/logging/configure
Content-Type: application/json

{
  "enable_performance": true,
  "enable_debug": true,
  "enable_trace": false,
  "enable_security": true,
  "enable_network": false,
  "enable_metrics": true,
  "slow_operation_threshold": 1.0,
  "metrics_log_interval": 10
}
```

#### **Test All Logging Categories**
```http
POST /api/crawl/logging/test
```

---

## 🧪 **Testing Workflow Examples**

### **Scenario 1: Basic UI Development**
```bash
# 1. Set minimal logging to reduce noise
curl -X POST http://localhost:8000/api/crawl/logging/preset/minimal

# 2. Create demo jobs
curl -X POST http://localhost:8000/api/crawl/demo

# 3. Start developing UI with clean, minimal logs
```

### **Scenario 2: Error Handling Testing**
```bash
# 1. Enable comprehensive error logging
curl -X POST http://localhost:8000/api/crawl/logging/preset/error_testing

# 2. Create job with high failure rate
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Error Test",
    "start_url": "https://test.com",
    "config": {"failure_rate": 0.3, "max_pages": 20}
  }'

# 3. Start job and observe error scenarios
curl -X POST http://localhost:8000/api/crawl/{job_id}/start
```

### **Scenario 3: Performance Analysis**
```bash
# 1. Enable performance logging
curl -X POST http://localhost:8000/api/crawl/logging/preset/performance

# 2. Create large job
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Performance Test",
    "start_url": "https://large-site.com",
    "config": {"max_pages": 500, "max_depth": 4}
  }'

# 3. Monitor performance metrics in logs
```

### **Scenario 4: Deep Debugging**
```bash
# 1. Enable full trace logging (very verbose!)
curl -X POST http://localhost:8000/api/crawl/logging/preset/full_trace

# 2. Create small job for detailed analysis
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Debug Test",
    "start_url": "https://debug.com",
    "config": {"max_pages": 10, "max_depth": 2}
  }'

# 3. Analyze detailed execution flow
```

---

## 📈 **Log Output Examples**

### **Performance Logging**
```json
{
  "timestamp": "2025-08-28T18:30:15.123456Z",
  "level": "info",
  "category": "performance",
  "message": "Operation completed: create_job",
  "operation": "create_job",
  "duration_seconds": 0.045,
  "job_id": "abc123",
  "timer_id": "create_job_def456"
}
```

### **Error Logging**
```json
{
  "timestamp": "2025-08-28T18:30:20.789012Z",
  "level": "error",
  "category": "error",
  "message": "Simulated http_error error",
  "error_type": "http_error",
  "url": "https://example.com/page1",
  "job_id": "abc123",
  "status_code": 404,
  "error_message": "Not Found",
  "is_simulated": true
}
```

### **State Transition Logging**
```json
{
  "timestamp": "2025-08-28T18:30:25.345678Z",
  "level": "info",
  "category": "state",
  "message": "job state transition",
  "entity": "job",
  "entity_id": "abc123",
  "old_state": "queued",
  "new_state": "running",
  "transition": "queued -> running"
}
```

### **Network Logging**
```json
{
  "timestamp": "2025-08-28T18:30:30.901234Z",
  "level": "info",
  "category": "network",
  "message": "Network response received",
  "url": "https://example.com/page2",
  "status_code": 200,
  "response_time_ms": 245.6,
  "content_length": 15420,
  "is_success": true,
  "job_id": "abc123"
}
```

### **Metrics Logging**
```json
{
  "timestamp": "2025-08-28T18:30:35.567890Z",
  "level": "info",
  "category": "metrics",
  "message": "Metrics snapshot",
  "job_id": "abc123",
  "pages_total": 25,
  "pages_crawled": 15,
  "pages_pending": 8,
  "pages_failed": 2,
  "pages_per_second": 2.3,
  "success_rate": 88.2,
  "progress_percentage": 60.0
}
```

---

## 🎯 **Best Practices**

### **Logging Strategy Recommendations**

1. **Start Minimal**: Begin with `minimal` preset for basic development
2. **Target Specific Areas**: Use focused presets (`network`, `performance`) for specific testing
3. **Debug Incrementally**: Use `debug` preset for general troubleshooting
4. **Trace Sparingly**: Only use `full_trace` for deep investigation of specific issues
5. **Production Testing**: Use `production` preset to simulate real-world logging

### **Logging Switch Behavior**

**Configuration changes are always visible:**
- Logs BEFORE switching (using current logger settings)
- Logs AFTER switching (using new logger settings)
- Uses both SimWeaver enhanced logger AND main API logger
- Special `log_config_change()` method bypasses category filtering
- Ensures you can always see when and how logging configuration changed

**Switch Log Locations:**
- **SimWeaver Enhanced Logger**: Detailed config comparison (if enabled)
- **Main API Logger**: Always visible basic switch information
- **Base Logger**: Configuration changes (always visible)

### **Performance Considerations**

- **Trace Logging**: Can significantly impact performance - use only when needed
- **Network Logging**: High volume - disable for performance testing
- **Metrics Interval**: Adjust `metrics_log_interval` based on job size
- **File Rotation**: Monitor log file sizes with verbose presets
- **Switch Overhead**: Logging switches themselves are lightweight

### **Development Workflow**

1. **UI Development**: `minimal` → `debug` as needed
2. **Error Testing**: `error_testing` → `debug` for investigation
3. **Performance Testing**: `performance` → `minimal` for clean metrics
4. **Integration Testing**: `production` → `debug` for issues
5. **Deep Debugging**: `debug` → `full_trace` for complex problems

---

## 🔧 **Configuration Reference**

### **SimWeaverConfig Parameters**
- `max_pages`: Maximum pages to crawl (1-10000)
- `max_depth`: Maximum crawl depth (1-10)
- `delay_min/max`: Request delay range in seconds
- `failure_rate`: Simulated failure percentage (0.0-1.0)
- `simulation_speed`: Speed multiplier for testing

### **Logging Thresholds**
- `slow_operation_threshold`: Log operations slower than X seconds
- `metrics_log_interval`: Log metrics every N pages
- `security_event_rate`: Frequency of simulated security events

### **Preset Selection Guide**
- **Learning/Exploring**: `debug`
- **UI Development**: `minimal` → `debug`
- **Error Handling**: `error_testing`
- **Performance Work**: `performance`
- **Network Issues**: `network`
- **State Problems**: `state_tracking`
- **Complex Bugs**: `full_trace`
- **Production Sim**: `production`

---

**SimWeaver provides a comprehensive, controllable environment for developing and testing SpigaUI while serving as an excellent testbed for our logging infrastructure. The granular logging control allows you to focus on specific aspects of the system without being overwhelmed by irrelevant log messages.**
