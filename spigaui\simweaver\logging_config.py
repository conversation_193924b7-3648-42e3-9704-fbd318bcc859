"""
SimWeaver logging configuration and utilities.

Provides comprehensive logging capabilities for testing and debugging
the SpigaUI logging system using SimWeaver as a testbed.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional
import structlog

from spigaui.core.logging import get_logger


class LogCategory(Enum):
    """Logging categories for SimWeaver."""
    PERFORMANCE = "performance"
    ERROR = "error"
    DEBUG = "debug"
    TRACE = "trace"
    SECURITY = "security"
    NETWORK = "network"
    STATE = "state"
    METRICS = "metrics"


@dataclass
class SimWeaverLoggingConfig:
    """Configuration for SimWeaver logging."""
    
    # Enable/disable logging categories
    enable_performance: bool = True
    enable_error: bool = True
    enable_debug: bool = True
    enable_trace: bool = False  # Very verbose
    enable_security: bool = True
    enable_network: bool = True
    enable_state: bool = True
    enable_metrics: bool = True
    
    # Performance logging thresholds
    slow_operation_threshold: float = 1.0  # Log operations slower than this
    memory_usage_threshold: int = 100 * 1024 * 1024  # 100MB
    
    # Error logging configuration
    log_all_errors: bool = True
    log_simulated_errors: bool = True
    log_network_errors: bool = True
    
    # Debug logging configuration
    log_page_discovery: bool = True
    log_url_generation: bool = True
    log_state_transitions: bool = True
    
    # Trace logging (very detailed)
    trace_crawl_flow: bool = False
    trace_data_generation: bool = False
    trace_statistics_updates: bool = False
    
    # Security event simulation
    simulate_security_events: bool = True
    security_event_rate: float = 0.01  # 1% of operations
    
    # Network simulation logging
    log_request_details: bool = True
    log_response_details: bool = True
    log_connection_issues: bool = True
    
    # Metrics logging frequency
    metrics_log_interval: int = 10  # Log metrics every N pages
    
    def is_enabled(self, category: LogCategory) -> bool:
        """Check if a logging category is enabled."""
        mapping = {
            LogCategory.PERFORMANCE: self.enable_performance,
            LogCategory.ERROR: self.enable_error,
            LogCategory.DEBUG: self.enable_debug,
            LogCategory.TRACE: self.enable_trace,
            LogCategory.SECURITY: self.enable_security,
            LogCategory.NETWORK: self.enable_network,
            LogCategory.STATE: self.enable_state,
            LogCategory.METRICS: self.enable_metrics,
        }
        return mapping.get(category, False)

    @classmethod
    def get_preset(cls, preset_name: str) -> "SimWeaverLoggingConfig":
        """Get a predefined logging configuration preset for different testing scenarios."""
        presets = {
            # Minimal logging - only errors and basic info
            "minimal": cls(
                enable_performance=False,
                enable_error=True,
                enable_debug=False,
                enable_trace=False,
                enable_security=False,
                enable_network=False,
                enable_state=False,
                enable_metrics=False,
                log_all_errors=True,
                log_simulated_errors=False,
                log_network_errors=False
            ),

            # Performance testing - focus on timing and metrics
            "performance": cls(
                enable_performance=True,
                enable_error=True,
                enable_debug=False,
                enable_trace=False,
                enable_security=False,
                enable_network=False,
                enable_state=False,
                enable_metrics=True,
                slow_operation_threshold=0.5,
                metrics_log_interval=5
            ),

            # Error testing - comprehensive error logging
            "error_testing": cls(
                enable_performance=False,
                enable_error=True,
                enable_debug=True,
                enable_trace=False,
                enable_security=True,
                enable_network=True,
                enable_state=True,
                enable_metrics=False,
                log_all_errors=True,
                log_simulated_errors=True,
                log_network_errors=True,
                simulate_security_events=True,
                security_event_rate=0.1
            ),

            # Network testing - focus on request/response simulation
            "network": cls(
                enable_performance=False,
                enable_error=True,
                enable_debug=False,
                enable_trace=False,
                enable_security=False,
                enable_network=True,
                enable_state=False,
                enable_metrics=False,
                log_request_details=True,
                log_response_details=True,
                log_connection_issues=True
            ),

            # State tracking - focus on state transitions
            "state_tracking": cls(
                enable_performance=False,
                enable_error=True,
                enable_debug=True,
                enable_trace=False,
                enable_security=False,
                enable_network=False,
                enable_state=True,
                enable_metrics=False,
                log_page_discovery=True,
                log_state_transitions=True
            ),

            # Debug mode - comprehensive debugging without trace
            "debug": cls(
                enable_performance=True,
                enable_error=True,
                enable_debug=True,
                enable_trace=False,
                enable_security=True,
                enable_network=True,
                enable_state=True,
                enable_metrics=True,
                log_page_discovery=True,
                log_url_generation=True,
                log_state_transitions=True,
                log_request_details=False,  # Reduce network noise
                log_response_details=False
            ),

            # Full trace - everything enabled (very verbose)
            "full_trace": cls(
                enable_performance=True,
                enable_error=True,
                enable_debug=True,
                enable_trace=True,
                enable_security=True,
                enable_network=True,
                enable_state=True,
                enable_metrics=True,
                trace_crawl_flow=True,
                trace_data_generation=True,
                trace_statistics_updates=True,
                log_page_discovery=True,
                log_url_generation=True,
                log_state_transitions=True,
                log_request_details=True,
                log_response_details=True,
                metrics_log_interval=1  # Very frequent
            ),

            # Production simulation - balanced logging
            "production": cls(
                enable_performance=True,
                enable_error=True,
                enable_debug=False,
                enable_trace=False,
                enable_security=True,
                enable_network=False,
                enable_state=False,
                enable_metrics=True,
                slow_operation_threshold=2.0,
                metrics_log_interval=20,
                simulate_security_events=False
            )
        }

        if preset_name not in presets:
            available = ", ".join(presets.keys())
            raise ValueError(f"Unknown logging preset '{preset_name}'. Available presets: {available}")

        return presets[preset_name]

    @classmethod
    def list_presets(cls) -> Dict[str, str]:
        """List available logging presets with descriptions."""
        return {
            "minimal": "Only errors and basic info - least verbose",
            "performance": "Performance timing and metrics only",
            "error_testing": "Comprehensive error and security logging",
            "network": "Network request/response simulation focus",
            "state_tracking": "State transitions and page discovery",
            "debug": "Comprehensive debugging without trace",
            "full_trace": "Everything enabled - most verbose",
            "production": "Balanced logging for production simulation"
        }


class SimWeaverLogger:
    """Enhanced logger for SimWeaver with category-based logging."""
    
    def __init__(self, config: SimWeaverLoggingConfig):
        self.config = config
        self.base_logger = get_logger("simweaver")
        
        # Category-specific loggers
        self.loggers = {
            LogCategory.PERFORMANCE: get_logger("simweaver.performance"),
            LogCategory.ERROR: get_logger("simweaver.error"),
            LogCategory.DEBUG: get_logger("simweaver.debug"),
            LogCategory.TRACE: get_logger("simweaver.trace"),
            LogCategory.SECURITY: get_logger("simweaver.security"),
            LogCategory.NETWORK: get_logger("simweaver.network"),
            LogCategory.STATE: get_logger("simweaver.state"),
            LogCategory.METRICS: get_logger("simweaver.metrics"),
        }
    
    def log(self, category: LogCategory, level: str, message: str, **kwargs):
        """Log a message in a specific category."""
        if not self.config.is_enabled(category):
            return
        
        logger = self.loggers.get(category, self.base_logger)
        log_method = getattr(logger, level.lower(), logger.info)
        
        # Add category to context
        kwargs['category'] = category.value
        
        log_method(message, **kwargs)
    
    def performance(self, message: str, **kwargs):
        """Log performance-related information."""
        self.log(LogCategory.PERFORMANCE, "info", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error information."""
        self.log(LogCategory.ERROR, "error", message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """Log debug information."""
        self.log(LogCategory.DEBUG, "debug", message, **kwargs)
    
    def trace(self, message: str, **kwargs):
        """Log trace information (very detailed)."""
        self.log(LogCategory.TRACE, "debug", message, **kwargs)
    
    def security(self, message: str, **kwargs):
        """Log security-related events."""
        self.log(LogCategory.SECURITY, "warning", message, **kwargs)
    
    def network(self, message: str, **kwargs):
        """Log network-related information."""
        self.log(LogCategory.NETWORK, "info", message, **kwargs)
    
    def state(self, message: str, **kwargs):
        """Log state transition information."""
        self.log(LogCategory.STATE, "info", message, **kwargs)
    
    def metrics(self, message: str, **kwargs):
        """Log metrics and statistics."""
        self.log(LogCategory.METRICS, "info", message, **kwargs)
    
    def log_operation_start(self, operation: str, **context):
        """Log the start of an operation."""
        if self.config.enable_trace:
            self.trace(f"Operation started: {operation}", operation=operation, **context)
    
    def log_operation_end(self, operation: str, duration: float, **context):
        """Log the end of an operation with timing."""
        if duration > self.config.slow_operation_threshold:
            self.performance(
                f"Slow operation completed: {operation}",
                operation=operation,
                duration_seconds=duration,
                is_slow=True,
                **context
            )
        elif self.config.enable_trace:
            self.trace(
                f"Operation completed: {operation}",
                operation=operation,
                duration_seconds=duration,
                **context
            )
    
    def log_page_discovered(self, url: str, depth: int, parent_url: str = None):
        """Log when a new page is discovered."""
        if self.config.log_page_discovery:
            self.debug(
                "Page discovered",
                url=url,
                depth=depth,
                parent_url=parent_url,
                discovery_method="link_extraction"
            )
    
    def log_state_transition(self, entity: str, entity_id: str, old_state: str, new_state: str):
        """Log state transitions."""
        if self.config.log_state_transitions:
            self.state(
                f"{entity} state transition",
                entity=entity,
                entity_id=entity_id,
                old_state=old_state,
                new_state=new_state,
                transition=f"{old_state} -> {new_state}"
            )
    
    def log_simulated_error(self, error_type: str, url: str, **details):
        """Log simulated errors for testing."""
        if self.config.log_simulated_errors:
            self.error(
                f"Simulated {error_type} error",
                error_type=error_type,
                url=url,
                is_simulated=True,
                **details
            )
    
    def log_network_request(self, url: str, method: str = "GET", **details):
        """Log network request details."""
        if self.config.log_request_details:
            self.network(
                "Network request initiated",
                url=url,
                method=method,
                request_type="simulated",
                **details
            )
    
    def log_network_response(self, url: str, status_code: int, response_time: float, **details):
        """Log network response details."""
        if self.config.log_response_details:
            self.network(
                "Network response received",
                url=url,
                status_code=status_code,
                response_time_ms=response_time * 1000,
                is_success=200 <= status_code < 300,
                **details
            )
    
    def log_security_event(self, event_type: str, **details):
        """Log security-related events."""
        self.security(
            f"Security event: {event_type}",
            event_type=event_type,
            is_simulated=True,
            **details
        )
    
    def log_metrics_snapshot(self, job_id: str, metrics: Dict[str, Any]):
        """Log a snapshot of current metrics."""
        self.metrics(
            "Metrics snapshot",
            job_id=job_id,
            **metrics
        )

    def log_config_change(self, change_type: str, old_config: Dict[str, Any], new_config: Dict[str, Any]):
        """
        Log configuration changes - always logs regardless of current settings.

        This uses the base logger to ensure configuration changes are always visible,
        even when switching to presets that disable certain logging categories.
        """
        self.base_logger.info(
            f"SimWeaver configuration {change_type}",
            change_type=change_type,
            old_config=old_config,
            new_config=new_config,
            config_change=True,
            always_visible=True
        )
