"""
Configuration management for SpigaUI.

Uses Pydantic settings for environment-based configuration.
"""

from functools import lru_cache
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )
    
    # Application settings
    debug: bool = Field(default=False, description="Enable debug mode")
    host: str = Field(default="127.0.0.1", description="Host to bind to")
    port: int = Field(default=8000, description="Port to bind to")
    log_level: str = Field(default="INFO", description="Logging level")
    serve_static: bool = Field(default=True, description="Serve static frontend files")

    # Logging settings
    enable_file_logging: bool = Field(default=True, description="Enable file logging")
    log_file_format: str = Field(default="json", description="Log file format (json or text)")
    log_max_size_mb: int = Field(default=10, description="Maximum log file size in MB")
    log_backup_count: int = Field(default=5, description="Number of backup log files to keep")
    enable_docs: bool = Field(default=True, description="Enable API documentation endpoints")
    
    # CORS settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="Allowed CORS origins"
    )
    
    # Redis settings (for Celery)
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        description="Redis connection URL"
    )
    
    # SpigaMonde integration
    spigamonde_database_url: str = Field(
        default="sqlite:///spigamonde.db",
        description="SpigaMonde database URL"
    )
    
    # Celery settings
    celery_broker_url: str = Field(
        default="redis://localhost:6379/0",
        description="Celery broker URL"
    )
    celery_result_backend: str = Field(
        default="redis://localhost:6379/0",
        description="Celery result backend URL"
    )
    
    # Security settings
    secret_key: str = Field(
        default="your-secret-key-change-in-production",
        description="Secret key for JWT tokens"
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="Access token expiration time in minutes"
    )

    # Enhanced Logging settings
    log_dir: str = Field(default="logs", description="Log file directory")
    log_max_size_mb: int = Field(default=10, description="Maximum log file size in MB")
    log_backup_count: int = Field(default=5, description="Number of backup log files to keep")
    log_file_format: str = Field(default="json", description="Log format for file output (json/text)")
    log_to_file: bool = Field(default=True, description="Enable log file output")
    log_requests: bool = Field(default=True, description="Enable request/response logging")

    # SpigaMonde integration settings
    default_crawl_timeout: int = Field(default=3600, description="Default crawl timeout in seconds")
    max_concurrent_jobs: int = Field(default=5, description="Maximum concurrent crawl jobs")

    # Development settings
    enable_docs: bool = Field(default=True, description="Enable API documentation endpoints")
    enable_monitoring: bool = Field(default=False, description="Enable performance monitoring")
    frontend_build_dir: str = Field(default="frontend/dist", description="Frontend build directory")


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings()
