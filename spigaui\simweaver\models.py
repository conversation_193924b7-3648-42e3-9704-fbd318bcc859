"""
SimWeaver data models and enumerations.

Defines the core data structures used by the SimWeaver crawler simulator.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, Optional
from dataclasses import dataclass, field


class CrawlStatus(Enum):
    """Crawl job status enumeration."""
    QUEUED = "queued"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PageStatus(Enum):
    """Individual page crawl status."""
    PENDING = "pending"
    CRAWLING = "crawling"
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    BLOCKED = "blocked"


@dataclass
class CrawlPage:
    """Represents a single page in the crawl."""
    url: str
    status: PageStatus = PageStatus.PENDING
    status_code: Optional[int] = None
    content_length: Optional[int] = None
    crawl_time: Optional[float] = None
    error_message: Optional[str] = None
    discovered_at: datetime = field(default_factory=datetime.now)
    crawled_at: Optional[datetime] = None
    depth: int = 0
    parent_url: Optional[str] = None


@dataclass
class CrawlStats:
    """Crawl statistics and metrics."""
    total_pages: int = 0
    pages_crawled: int = 0
    pages_pending: int = 0
    pages_failed: int = 0
    pages_per_second: float = 0.0
    avg_response_time: float = 0.0
    total_data_downloaded: int = 0  # bytes
    unique_domains: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[float] = None


@dataclass
class CrawlJob:
    """Represents a complete crawl job."""
    job_id: str
    name: str
    start_url: str
    status: CrawlStatus = CrawlStatus.QUEUED
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Configuration
    max_pages: int = 100
    max_depth: int = 3
    delay_min: float = 0.1
    delay_max: float = 2.0
    failure_rate: float = 0.05  # 5% failure rate
    
    # Runtime data
    pages: Dict[str, CrawlPage] = field(default_factory=dict)
    stats: CrawlStats = field(default_factory=CrawlStats)
    error_message: Optional[str] = None
    
    # Simulation control
    _should_stop: bool = False
    _is_paused: bool = False
    
    def get_progress_percentage(self) -> float:
        """Calculate completion percentage."""
        if self.max_pages == 0:
            return 0.0
        
        if self.status == CrawlStatus.COMPLETED:
            return 100.0
        
        # Base progress on pages crawled vs max pages
        return min((self.stats.pages_crawled / self.max_pages) * 100, 100.0)
    
    def get_current_url(self) -> Optional[str]:
        """Get the currently crawling URL."""
        for page in self.pages.values():
            if page.status == PageStatus.CRAWLING:
                return page.url
        return None
    
    def get_success_rate(self) -> float:
        """Calculate success rate percentage."""
        total_attempted = self.stats.pages_crawled + self.stats.pages_failed
        if total_attempted == 0:
            return 0.0
        return (self.stats.pages_crawled / total_attempted) * 100
    
    def get_estimated_time_remaining(self) -> Optional[float]:
        """Estimate time remaining in seconds."""
        if (self.status not in [CrawlStatus.RUNNING, CrawlStatus.PAUSED] or 
            self.stats.pages_per_second <= 0):
            return None
        
        remaining_pages = max(0, self.max_pages - self.stats.pages_crawled)
        return remaining_pages / self.stats.pages_per_second
    
    def is_active(self) -> bool:
        """Check if the job is currently active."""
        return self.status in [CrawlStatus.STARTING, CrawlStatus.RUNNING, CrawlStatus.PAUSED]
    
    def can_be_started(self) -> bool:
        """Check if the job can be started."""
        return self.status == CrawlStatus.QUEUED
    
    def can_be_stopped(self) -> bool:
        """Check if the job can be stopped."""
        return self.status in [CrawlStatus.STARTING, CrawlStatus.RUNNING, CrawlStatus.PAUSED]
    
    def can_be_paused(self) -> bool:
        """Check if the job can be paused."""
        return self.status == CrawlStatus.RUNNING
    
    def can_be_resumed(self) -> bool:
        """Check if the job can be resumed."""
        return self.status == CrawlStatus.PAUSED
