# SpigaUI - Modern Web Interface for SpigaMonde

A clean, modern web interface for the SpigaMonde web crawler built with FastAPI, Celery, and React/Vue.

## 🚨 Emergency Stop Commands

**If SimWeaver crawler simulator is running amok:**

```bash
# Signal commands (recommended)
uv run sig.py kill                       # SIGTERM - Stop all jobs (safe)
uv run sig.py kill -9                    # SIGKILL - Nuclear reset (destroys all data)

# Direct API commands (alternative)
curl -X POST http://localhost:8000/api/crawl/emergency/stop-all
curl -X POST http://localhost:8000/api/crawl/emergency/kill-engine
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Node.js 18+ (for frontend)
- Redis (for Celery backend)
- UV package manager

### Backend Setup

```bash
# Activate virtual environment
.venv\Scripts\Activate.ps1

# Start the backend server
python -m spigaui.main



# Install UV if not already installed
pip install uv

# Create virtual environment and install dependencies
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\Activate.ps1
uv pip install -e .

# Install development dependencies
uv pip install -e ".[dev]"

# Start Redis (required for Celery)
redis-server

# Start the backend server
uv run uvicorn spigaui.main:app --reload --host 0.0.0.0 --port 8000

# Or use the simplified command
python -m spigaui.main
```

### Frontend Setup

```bash
cd frontend
npm install
npm run dev
```

## 🏗️ Architecture

### Backend (FastAPI + Celery)
- **API Server**: FastAPI with async support
- **Task Queue**: Celery for background crawl jobs
- **Message Broker**: Redis for task queuing and real-time updates
- **Real-time Updates**: Server-Sent Events (SSE) for progress tracking

### Frontend (React/Vue + TypeScript)
- **Framework**: React or Vue 3 with TypeScript
- **State Management**: Redux Toolkit or Pinia
- **Styling**: Tailwind CSS or similar
- **Build Tool**: Vite for fast development

### Integration
- **SpigaMonde Core**: Imported as library dependency
- **API-First**: Clean separation between UI and crawler logic
- **Type Safety**: Full TypeScript coverage with Pydantic models

## 📁 Project Structure

```
SpigaUI/
├── spigaui/              # Backend Python package
│   ├── api/              # FastAPI routes
│   ├── core/             # Business logic
│   ├── models/           # Pydantic models
│   ├── services/         # Service layer
│   └── workers/          # Celery tasks
├── frontend/             # Frontend application
│   ├── src/
│   │   ├── components/   # Reusable components
│   │   ├── pages/        # Page components
│   │   ├── services/     # API clients
│   │   └── types/        # TypeScript definitions
│   └── public/
├── tests/                # Test suite
├── docker/               # Docker configuration
└── docs/                 # Documentation
```

## 🔧 Development

### Code Quality
```bash
# Format code
uv run black spigaui tests
uv run isort spigaui tests

# Lint code
uv run flake8 spigaui tests
uv run mypy spigaui

# Run tests
uv run pytest
```

### Environment Variables
Create a `.env` file:
```env
# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# SpigaMonde Configuration
SPIGAMONDE_DATABASE_URL=sqlite:///spigamonde.db

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
```

## 🚀 Deployment

### Docker Compose
```bash
docker-compose up -d
```

### Manual Deployment
1. Set up Redis server
2. Configure environment variables
3. Run database migrations
4. Start Celery workers
5. Start FastAPI server
6. Build and serve frontend

## 🕷️ SimWeaver - Crawler Simulator

SpigaUI includes **SimWeaver**, a sophisticated web crawler simulator for development and testing.

### Quick Test Commands

```bash
# Start the server
python -m spigaui.main

# Create demo crawl jobs
curl -X POST http://localhost:8000/api/crawl/demo

# Check engine health
curl http://localhost:8000/api/crawl/status/engine

# Test logging system
curl -X POST http://localhost:8000/api/crawl/logging/test
```

### 🚨 Emergency Safety Commands

**If SimWeaver is running amok or consuming too many resources:**

```bash
# LEVEL 1: Stop all running jobs (safe)
curl -X POST http://localhost:8000/api/crawl/emergency/stop-all

# LEVEL 2: Nuclear reset - destroys ALL job data (destructive)
curl -X POST http://localhost:8000/api/crawl/emergency/kill-engine

# Monitor for problems
curl http://localhost:8000/api/crawl/status/engine
```

### Logging Presets

```bash
# List available logging presets
curl http://localhost:8000/api/crawl/logging/presets

# Switch to minimal logging (quiet)
curl -X POST http://localhost:8000/api/crawl/logging/preset/minimal

# Switch to debug logging (comprehensive)
curl -X POST http://localhost:8000/api/crawl/logging/preset/debug

# Switch to full trace (very verbose)
curl -X POST http://localhost:8000/api/crawl/logging/preset/full_trace
```

**Available Presets:**
- `minimal` - Only errors (quietest)
- `performance` - Performance metrics only
- `error_testing` - Comprehensive error scenarios
- `network` - Network request/response focus
- `state_tracking` - State transitions
- `debug` - General debugging (recommended)
- `full_trace` - Everything enabled (very verbose)
- `production` - Production-like logging

## 📋 **Quick Command Reference**

### **Signal Commands (Recommended)**
```bash
# Linux-like signal interface (cross-platform Python tool)
uv run sig.py start                      # Start SimWeaver
uv run sig.py demo                       # Create demo jobs
uv run sig.py status                     # Check status
uv run sig.py kill                       # Emergency stop (SIGTERM)
uv run sig.py kill -9                    # Nuclear reset (SIGKILL)
uv run sig.py preset debug               # Set debug logging
uv run sig.py logs --follow              # Follow logs in real-time

# Alternative: Direct Python execution
python sig.py status                     # Also works
```

### **Direct API Commands (Alternative)**
```bash
# Start SimWeaver
uv run python -m spigaui.main

# Create demo jobs
curl -X POST http://localhost:8000/api/crawl/demo

# Check status
curl http://localhost:8000/api/crawl/status/engine

# Emergency stop
curl -X POST http://localhost:8000/api/crawl/emergency/stop-all

# Set debug logging
curl -X POST http://localhost:8000/api/crawl/logging/preset/debug
```

### **Job Control**
```bash
# List jobs
curl http://localhost:8000/api/crawl

# Start job
curl -X POST http://localhost:8000/api/crawl/{job_id}/start

# Stop job
curl -X POST http://localhost:8000/api/crawl/{job_id}/stop
```

### **Monitor Logs**
```bash
# Watch all logs
tail -f logs/spigaui.log

# Only errors
tail -f logs/spigaui.log | grep '"level": "error"'

# Only metrics
tail -f logs/spigaui.log | grep '"category": "metrics"'
```

**📖 Complete Command Reference**: [docs/simweaver_commands.md](docs/simweaver_commands.md)

## 📚 API Documentation

Once the server is running, visit:
- **API Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **SimWeaver Guide**: [docs/sim_weaver.md](docs/sim_weaver.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.
