"""
Pydantic models for crawl jobs and configurations.

Defines the data structures used for crawl job management.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, HttpUrl, Field


class JobStatus(str, Enum):
    """Crawl job status enumeration."""
    QUEUED = "queued"
    STARTING = "starting"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PageStatus(str, Enum):
    """Individual page status enumeration."""
    PENDING = "pending"
    CRAWLING = "crawling"
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    BLOCKED = "blocked"


class CrawlConfig(BaseModel):
    """Configuration for a crawl job."""
    max_depth: int = Field(default=3, ge=1, le=10, description="Maximum crawl depth")
    max_pages: int = Field(default=100, ge=1, le=10000, description="Maximum pages to crawl")
    delay_min: float = Field(default=0.1, ge=0.0, le=10.0, description="Minimum delay between requests")
    delay_max: float = Field(default=2.0, ge=0.1, le=10.0, description="Maximum delay between requests")
    follow_external: bool = Field(default=False, description="Follow external links")
    respect_robots: bool = Field(default=True, description="Respect robots.txt")
    user_agent: Optional[str] = Field(default=None, description="Custom user agent")
    headers: Dict[str, str] = Field(default_factory=dict, description="Custom headers")
    cookies: Dict[str, str] = Field(default_factory=dict, description="Custom cookies")
    timeout: int = Field(default=30, ge=5, le=300, description="Request timeout in seconds")
    retries: int = Field(default=3, ge=0, le=10, description="Number of retries for failed requests")
    failure_rate: float = Field(default=0.05, ge=0.0, le=1.0, description="Simulated failure rate (0.0-1.0)")


class SimWeaverConfigAPI(BaseModel):
    """SimWeaver configuration for API requests."""
    max_depth: int = Field(default=3, ge=1, le=10, description="Maximum crawl depth")
    max_pages: int = Field(default=100, ge=1, le=10000, description="Maximum pages to crawl")
    delay_min: float = Field(default=0.1, ge=0.0, le=10.0, description="Minimum delay between requests")
    delay_max: float = Field(default=2.0, ge=0.1, le=10.0, description="Maximum delay between requests")
    failure_rate: float = Field(default=0.05, ge=0.0, le=1.0, description="Simulated failure rate")


class CrawlJobCreate(BaseModel):
    """Model for creating a new crawl job."""
    name: str = Field(description="Job name")
    start_url: str = Field(description="Starting URL for the crawl")
    config: Optional[SimWeaverConfigAPI] = Field(default=None, description="Crawl configuration")
    description: Optional[str] = Field(default=None, description="Optional job description")


class CrawlJobStart(BaseModel):
    """Model for starting a crawl job."""
    job_id: str = Field(description="Job identifier to start")


class CrawlStats(BaseModel):
    """Crawl job statistics and metrics."""
    total_pages: int = Field(default=0, description="Total pages discovered")
    pages_crawled: int = Field(default=0, description="Number of pages crawled")
    pages_pending: int = Field(default=0, description="Number of pages pending")
    pages_failed: int = Field(default=0, description="Number of failed pages")
    pages_per_second: float = Field(default=0.0, description="Crawl rate")
    avg_response_time: float = Field(default=0.0, description="Average response time")
    total_data_downloaded: int = Field(default=0, description="Total bytes downloaded")
    unique_domains: int = Field(default=0, description="Number of unique domains")
    start_time: Optional[datetime] = Field(default=None, description="Crawl start time")
    end_time: Optional[datetime] = Field(default=None, description="Crawl end time")
    duration: Optional[float] = Field(default=None, description="Total duration in seconds")


class CrawlPage(BaseModel):
    """Individual page in a crawl job."""
    url: str = Field(description="Page URL")
    status: PageStatus = Field(description="Page crawl status")
    status_code: Optional[int] = Field(default=None, description="HTTP status code")
    content_length: Optional[int] = Field(default=None, description="Content length in bytes")
    crawl_time: Optional[float] = Field(default=None, description="Time taken to crawl")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")
    discovered_at: datetime = Field(description="When the page was discovered")
    crawled_at: Optional[datetime] = Field(default=None, description="When the page was crawled")
    depth: int = Field(default=0, description="Depth from start URL")
    parent_url: Optional[str] = Field(default=None, description="Parent page URL")


class CrawlJobSummary(BaseModel):
    """Summary view of a crawl job."""
    job_id: str = Field(description="Job identifier")
    name: str = Field(description="Job name")
    start_url: str = Field(description="Starting URL")
    status: JobStatus = Field(description="Current job status")
    created_at: datetime = Field(description="When the job was created")
    started_at: Optional[datetime] = Field(default=None, description="When the job started")
    completed_at: Optional[datetime] = Field(default=None, description="When the job completed")
    stats: CrawlStats = Field(description="Job statistics")


class CrawlJobDetail(BaseModel):
    """Detailed view of a crawl job."""
    job_id: str = Field(description="Job identifier")
    name: str = Field(description="Job name")
    start_url: str = Field(description="Starting URL")
    status: JobStatus = Field(description="Current job status")
    created_at: datetime = Field(description="When the job was created")
    started_at: Optional[datetime] = Field(default=None, description="When the job started")
    completed_at: Optional[datetime] = Field(default=None, description="When the job completed")
    error_message: Optional[str] = Field(default=None, description="Error message if failed")

    # Configuration
    max_pages: int = Field(description="Maximum pages to crawl")
    max_depth: int = Field(description="Maximum crawl depth")
    delay_min: float = Field(description="Minimum delay between requests")
    delay_max: float = Field(description="Maximum delay between requests")
    failure_rate: float = Field(description="Simulated failure rate")

    # Statistics and pages
    stats: CrawlStats = Field(description="Job statistics")
    pages: List[CrawlPage] = Field(default_factory=list, description="All pages in the job")

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }


class JobEvent(BaseModel):
    """Real-time job event model."""
    type: str = Field(description="Event type")
    job_id: str = Field(description="Job identifier")
    timestamp: datetime = Field(description="Event timestamp")
    data: Dict[str, Any] = Field(description="Event data")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
