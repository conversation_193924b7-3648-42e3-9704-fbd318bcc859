"""
SimWeaver - Simulated Spider for SpigaUI Development

A realistic web crawler simulator that generates fake crawling data
for UI development and testing purposes.

This module provides:
- Realistic crawling simulation with configurable parameters
- Various job states (queued, running, paused, completed, failed, cancelled)
- Realistic page generation with success/failure scenarios
- Performance metrics and statistics
- Real-time progress tracking
"""

from .engine import SimWeaver
from .models import (
    CrawlStatus,
    PageStatus,
    CrawlPage,
    CrawlStats,
    CrawlJob
)
from .data_generator import DataGenerator
from .config import SimWeaverConfig
from .logging_config import SimWeaverLoggingConfig, SimWeaverLogger

# Global SimWeaver instance with enhanced logging
simweaver = SimWeaver()

__all__ = [
    "SimWeaver",
    "simweaver",
    "CrawlStatus",
    "PageStatus",
    "CrawlPage",
    "CrawlStats",
    "CrawlJob",
    "DataGenerator",
    "SimWeaverConfig",
    "SimWeaverLoggingConfig",
    "SimWeaverLogger"
]
