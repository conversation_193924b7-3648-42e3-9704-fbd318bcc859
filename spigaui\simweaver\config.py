"""
SimWeaver configuration and settings.

Defines configuration options for the SimWeaver crawler simulator.
"""

from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class SimWeaverConfig:
    """Configuration for SimWeaver crawler simulation."""
    
    # Crawl limits
    max_pages: int = 100
    max_depth: int = 3
    
    # Timing configuration
    delay_min: float = 0.1  # Minimum delay between requests (seconds)
    delay_max: float = 2.0  # Maximum delay between requests (seconds)
    simulation_speed: float = 10.0  # Speed multiplier for simulation (higher = faster)
    
    # Failure simulation
    failure_rate: float = 0.05  # Percentage of pages that should fail (0.0-1.0)
    timeout_rate: float = 0.02  # Percentage of pages that should timeout
    blocked_rate: float = 0.01  # Percentage of pages that should be blocked
    
    # Content generation
    min_content_size: int = 1024  # Minimum page size in bytes (1KB)
    max_content_size: int = 102400  # Maximum page size in bytes (100KB)
    min_links_per_page: int = 1  # Minimum links to generate per page
    max_links_per_page: int = 5  # Maximum links to generate per page
    
    # Domain and URL generation
    use_realistic_domains: bool = True
    custom_domains: List[str] = None
    generate_subdomains: bool = True
    follow_external_links: bool = False
    
    # Performance simulation
    simulate_slow_pages: bool = True
    slow_page_threshold: float = 2.0  # Pages slower than this are considered slow
    slow_page_rate: float = 0.1  # Percentage of pages that should be slow
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        if self.custom_domains is None:
            self.custom_domains = []
        
        # Validate ranges
        if not 0.0 <= self.failure_rate <= 1.0:
            raise ValueError("failure_rate must be between 0.0 and 1.0")
        
        if not 0.0 <= self.timeout_rate <= 1.0:
            raise ValueError("timeout_rate must be between 0.0 and 1.0")
        
        if not 0.0 <= self.blocked_rate <= 1.0:
            raise ValueError("blocked_rate must be between 0.0 and 1.0")
        
        if self.delay_min < 0:
            raise ValueError("delay_min must be non-negative")
        
        if self.delay_max < self.delay_min:
            raise ValueError("delay_max must be >= delay_min")
        
        if self.max_pages <= 0:
            raise ValueError("max_pages must be positive")
        
        if self.max_depth <= 0:
            raise ValueError("max_depth must be positive")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "max_pages": self.max_pages,
            "max_depth": self.max_depth,
            "delay_min": self.delay_min,
            "delay_max": self.delay_max,
            "simulation_speed": self.simulation_speed,
            "failure_rate": self.failure_rate,
            "timeout_rate": self.timeout_rate,
            "blocked_rate": self.blocked_rate,
            "min_content_size": self.min_content_size,
            "max_content_size": self.max_content_size,
            "min_links_per_page": self.min_links_per_page,
            "max_links_per_page": self.max_links_per_page,
            "use_realistic_domains": self.use_realistic_domains,
            "custom_domains": self.custom_domains,
            "generate_subdomains": self.generate_subdomains,
            "follow_external_links": self.follow_external_links,
            "simulate_slow_pages": self.simulate_slow_pages,
            "slow_page_threshold": self.slow_page_threshold,
            "slow_page_rate": self.slow_page_rate,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SimWeaverConfig":
        """Create configuration from dictionary."""
        return cls(**data)
    
    @classmethod
    def get_preset(cls, preset_name: str) -> "SimWeaverConfig":
        """Get a predefined configuration preset."""
        presets = {
            "fast": cls(
                max_pages=50,
                max_depth=2,
                delay_min=0.05,
                delay_max=0.5,
                simulation_speed=20.0,
                failure_rate=0.02,
            ),
            "normal": cls(
                max_pages=100,
                max_depth=3,
                delay_min=0.1,
                delay_max=2.0,
                simulation_speed=10.0,
                failure_rate=0.05,
            ),
            "slow": cls(
                max_pages=200,
                max_depth=4,
                delay_min=0.5,
                delay_max=5.0,
                simulation_speed=5.0,
                failure_rate=0.1,
                simulate_slow_pages=True,
                slow_page_rate=0.2,
            ),
            "large": cls(
                max_pages=1000,
                max_depth=5,
                delay_min=0.1,
                delay_max=1.0,
                simulation_speed=15.0,
                failure_rate=0.08,
            ),
            "unreliable": cls(
                max_pages=100,
                max_depth=3,
                delay_min=0.2,
                delay_max=3.0,
                simulation_speed=8.0,
                failure_rate=0.15,
                timeout_rate=0.05,
                blocked_rate=0.03,
            ),
        }
        
        if preset_name not in presets:
            available = ", ".join(presets.keys())
            raise ValueError(f"Unknown preset '{preset_name}'. Available presets: {available}")
        
        return presets[preset_name]
