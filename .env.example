# SpigaUI Environment Configuration
# Copy this file to .env and update the values as needed

# =============================================================================
# Application Settings
# =============================================================================

# Enable debug mode (true/false)
DEBUG=true

# Host and port to bind the server to
HOST=127.0.0.1
PORT=8000

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Serve static frontend files (true/false)
SERVE_STATIC=true

# =============================================================================
# CORS Settings
# =============================================================================

# Allowed CORS origins (JSON array format)
# For development, include your frontend dev server
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:5173"]

# =============================================================================
# Redis Configuration
# =============================================================================

# Redis connection URL for caching and real-time features
REDIS_URL=redis://localhost:6379/0

# =============================================================================
# Celery Configuration
# =============================================================================

# Celery broker URL (usually same as Redis)
CELERY_BROKER_URL=redis://localhost:6379/0

# Celery result backend URL
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# Database Configuration
# =============================================================================

# SpigaMonde database URL
# For development: sqlite:///./spigamonde.db
# For production: postgresql://user:password@localhost/spigamonde
SPIGAMONDE_DATABASE_URL=sqlite:///./spigamonde.db

# =============================================================================
# Security Settings
# =============================================================================

# Secret key for JWT tokens (CHANGE THIS IN PRODUCTION!)
# Generate with: python -c "import secrets; print(secrets.token_urlsafe(32))"
SECRET_KEY=your-secret-key-change-in-production

# Access token expiration time in minutes
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# Enhanced Logging Configuration
# =============================================================================

# Log file directory (relative to project root)
LOG_DIR=logs

# Maximum log file size in MB
LOG_MAX_SIZE_MB=10

# Number of backup log files to keep
LOG_BACKUP_COUNT=5

# Log format for file output (json/text)
LOG_FILE_FORMAT=json

# Enable log file output (true/false)
LOG_TO_FILE=true

# Enable request/response logging (true/false)
LOG_REQUESTS=true

# =============================================================================
# SpigaMonde Integration
# =============================================================================

# Default crawl timeout in seconds
DEFAULT_CRAWL_TIMEOUT=3600

# Maximum concurrent crawl jobs
MAX_CONCURRENT_JOBS=5

# =============================================================================
# Development Settings
# =============================================================================

# Enable API documentation endpoints (true/false)
ENABLE_DOCS=true

# Enable performance monitoring (true/false)
ENABLE_MONITORING=false

# Frontend build directory (relative to project root)
FRONTEND_BUILD_DIR=frontend/dist
