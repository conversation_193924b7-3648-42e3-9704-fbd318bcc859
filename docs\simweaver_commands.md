# SimWeaver Command Reference

Quick reference for controlling SimWeaver crawler simulator via the `sig.py` command tool and API commands.

---

## 🚀 **Quick Start (Recommended)**

### **Start Development Servers**
```bash
# Start both backend and frontend servers
uv run python sig.py start

# Start backend only
uv run python sig.py start --backend

# Start frontend only
uv run python sig.py start --frontend

# Servers will be available at:
# Backend:  http://127.0.0.1:8000
# Frontend: http://localhost:3000
# API Docs: http://127.0.0.1:8000/api/docs
```

### **Restart Development Servers**
```bash
# Restart both backend and frontend servers
uv run python sig.py restart

# Restart backend only (useful after code changes)
uv run python sig.py restart --backend

# Restart frontend only (useful after config changes)
uv run python sig.py restart --frontend

# Smart process management:
# 1. Gracefully kills existing processes
# 2. Waits for cleanup (2 seconds)
# 3. Starts requested servers
```

### **Stop Servers**
```bash
# Kill server processes (recommended)
uv run python sig.py kill --procs

# Force kill server processes
uv run python sig.py kill --procs -9

# Alternative: Stop jobs via API (doesn't kill processes)
uv run python sig.py kill

# Alternative: Nuclear reset via API (destroys data)
uv run python sig.py kill -9
```

### **Requirements**
```bash
# For process management features, install psutil:
uv add psutil

# Already included in project dependencies
```

### **🔄 Restart vs Kill + Start**
The `restart` command is more efficient than manually killing and starting:

**Traditional approach:**
```bash
uv run python sig.py kill --procs    # Kill processes
uv run python sig.py start           # Start servers
```

**New restart approach:**
```bash
uv run python sig.py restart         # One command does both
```

**Benefits of restart:**
- ✅ **Faster**: Single command with optimized timing
- ✅ **Safer**: Graceful shutdown before restart
- ✅ **Selective**: Restart only what you need
- ✅ **Reliable**: Handles process cleanup automatically

---

## 🚀 **Manual Server Start (Alternative)**

### **Start the Backend Server**
```bash
# Start SimWeaver server (includes auto-reload for development)
uv run python -m spigaui.main

# Server will be available at: http://localhost:8000
# API Documentation: http://localhost:8000/docs
```

### **Verify Server is Running**
```bash
# Health check
curl http://localhost:8000/api/health

# Engine status
curl http://localhost:8000/api/crawl/status/engine
```

---

## 🕷️ **Creating & Managing Crawl Jobs**

### **Quick Demo Jobs**
```bash
# Create 4 demo jobs (one auto-starts)
curl -X POST http://localhost:8000/api/crawl/demo

# Response: Creates Fast Demo, Normal Demo, Large Site, and Unreliable Network jobs
```

### **Create Custom Job**
```bash
# Basic job
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My Test Crawl",
    "start_url": "https://example.com",
    "config": {
      "max_pages": 50,
      "max_depth": 3,
      "delay_min": 0.1,
      "delay_max": 1.0,
      "failure_rate": 0.05
    }
  }'

# High-error job for testing
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Error Test",
    "start_url": "https://test.com",
    "config": {
      "max_pages": 20,
      "failure_rate": 0.3,
      "delay_min": 0.05,
      "delay_max": 0.5
    }
  }'
```

### **Job Control**
```bash
# List all jobs
curl http://localhost:8000/api/crawl

# Get specific job details
curl http://localhost:8000/api/crawl/{job_id}

# Start a job
curl -X POST http://localhost:8000/api/crawl/{job_id}/start

# Pause a job
curl -X POST http://localhost:8000/api/crawl/{job_id}/pause

# Resume a job
curl -X POST http://localhost:8000/api/crawl/{job_id}/resume

# Stop a job
curl -X POST http://localhost:8000/api/crawl/{job_id}/stop
```

---

## 🚨 **Emergency Controls**

### **🛑 Level 1: Stop All Jobs (Safe)**
```bash
# Gracefully stop all running jobs
curl -X POST http://localhost:8000/api/crawl/emergency/stop-all

# Use when: Jobs consuming too many resources
# Effect: Stops execution, preserves job data
```

### **💥 Level 2: Nuclear Reset (Destructive)**
```bash
# Complete engine reset - DESTROYS ALL DATA
curl -X POST http://localhost:8000/api/crawl/emergency/kill-engine

# Use when: Engine corrupted or stop-all fails
# Effect: Clears all jobs, timers, and state
```

### **📊 Health Monitoring**
```bash
# Check engine health
curl http://localhost:8000/api/crawl/status/engine

# Response includes warnings for:
# - High job counts (>10 active)
# - High timer counts (>50 active)  
# - Stuck jobs (>1 hour running)
# - Very high total jobs (>1000)
```

---

## 🎛️ **Logging Control**

### **List Available Presets**
```bash
# See all logging presets
curl http://localhost:8000/api/crawl/logging/presets
```

### **Quick Preset Switching**
```bash
# Minimal logging (quietest)
curl -X POST http://localhost:8000/api/crawl/logging/preset/minimal

# Debug logging (recommended for development)
curl -X POST http://localhost:8000/api/crawl/logging/preset/debug

# Performance testing
curl -X POST http://localhost:8000/api/crawl/logging/preset/performance

# Error testing
curl -X POST http://localhost:8000/api/crawl/logging/preset/error_testing

# Network focus
curl -X POST http://localhost:8000/api/crawl/logging/preset/network

# Full trace (very verbose)
curl -X POST http://localhost:8000/api/crawl/logging/preset/full_trace

# Production simulation
curl -X POST http://localhost:8000/api/crawl/logging/preset/production
```

### **Custom Logging Configuration**
```bash
# Custom settings
curl -X POST http://localhost:8000/api/crawl/logging/configure \
  -H "Content-Type: application/json" \
  -d '{
    "enable_performance": true,
    "enable_debug": true,
    "enable_trace": false,
    "enable_security": true,
    "enable_network": false,
    "enable_metrics": true,
    "slow_operation_threshold": 1.0,
    "metrics_log_interval": 10
  }'
```

### **Test Logging System**
```bash
# Generate sample logs in all categories
curl -X POST http://localhost:8000/api/crawl/logging/test
```

---

## 📋 **Common Workflows**

### **🔧 Development Workflow (Recommended)**
```bash
# 1. Start both servers
uv run python sig.py start

# 2. Set debug logging
uv run python sig.py preset debug

# 3. Create demo jobs
uv run python sig.py demo

# 4. Monitor logs
uv run python sig.py logs --follow

# 5. Restart after changes (much faster than kill + start)
uv run python sig.py restart --frontend  # After config changes
uv run python sig.py restart --backend   # After code changes
uv run python sig.py restart             # Full restart

# 6. When done, kill processes
uv run python sig.py kill --procs
```

### **🔧 Backend-Only Development Workflow**
```bash
# 1. Start backend server only
uv run python sig.py start --backend

# 2. Set debug logging
uv run python sig.py preset debug

# 3. Create demo jobs
uv run python sig.py demo

# 4. Monitor logs in: logs/spigaui.log
```

### **🧪 Error Testing Workflow**
```bash
# 1. Enable error logging
curl -X POST http://localhost:8000/api/crawl/logging/preset/error_testing

# 2. Create high-error job
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{"name": "Error Test", "start_url": "https://test.com", "config": {"failure_rate": 0.4, "max_pages": 15}}'

# 3. Start job and observe error scenarios
curl -X POST http://localhost:8000/api/crawl/{job_id}/start
```

### **⚡ Performance Testing Workflow**
```bash
# 1. Enable performance logging
curl -X POST http://localhost:8000/api/crawl/logging/preset/performance

# 2. Create large job
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{"name": "Performance Test", "start_url": "https://large-site.com", "config": {"max_pages": 500, "max_depth": 4}}'

# 3. Monitor performance metrics
curl http://localhost:8000/api/crawl/{job_id}
```

### **🔍 Deep Debugging Workflow**
```bash
# 1. Enable full trace (very verbose!)
curl -X POST http://localhost:8000/api/crawl/logging/preset/full_trace

# 2. Create small job for detailed analysis
curl -X POST http://localhost:8000/api/crawl \
  -H "Content-Type: application/json" \
  -d '{"name": "Debug Test", "start_url": "https://debug.com", "config": {"max_pages": 10, "max_depth": 2}}'

# 3. Analyze detailed execution flow in logs
```

---

## 🎯 **Signal Command Tool (Recommended)**

### **Linux-like Signal Interface**
```bash
# Cross-platform Python tool with argparse (uv recommended)
uv run python sig.py --help             # Show all commands

# Development server management
uv run python sig.py start              # Start both backend and frontend
uv run python sig.py start --backend    # Start backend only
uv run python sig.py start --frontend   # Start frontend only
uv run python sig.py restart            # Restart both servers
uv run python sig.py restart --backend  # Restart backend only
uv run python sig.py restart --frontend # Restart frontend only

# Process control
uv run python sig.py kill --procs       # Kill server processes (SIGTERM)
uv run python sig.py kill --procs -9    # Force kill processes (SIGKILL)

# API-based job control
uv run python sig.py kill               # Emergency stop jobs (API)
uv run python sig.py kill -9            # Nuclear reset (API, destroys data)

# Status and monitoring
uv run python sig.py status             # Check engine status
uv run python sig.py demo               # Create demo jobs
uv run python sig.py jobs               # List all jobs

# Logging control
uv run python sig.py preset debug       # Set logging preset
uv run python sig.py logs --follow      # Follow logs in real-time
uv run python sig.py logs --filter error # Follow error logs only

# Alternative: Direct Python execution (if in activated environment)
python sig.py status                     # Also works without uv
```

## 🎯 **Quick Reference Card**

### **Signal Commands (Recommended)**
```bash
# START SERVERS
uv run python sig.py start              # Both backend + frontend
uv run python sig.py start --backend    # Backend only
uv run python sig.py start --frontend   # Frontend only

# RESTART SERVERS
uv run python sig.py restart            # Restart both servers
uv run python sig.py restart --backend  # Restart backend only
uv run python sig.py restart --frontend # Restart frontend only

# KILL PROCESSES
uv run python sig.py kill --procs       # Kill server processes
uv run python sig.py kill --procs -9    # Force kill processes

# JOB CONTROL (API)
uv run python sig.py demo               # Create demo jobs
uv run python sig.py status             # Check engine status
uv run python sig.py kill               # Stop jobs (API)
uv run python sig.py kill -9            # Nuclear reset (API)

# LOGGING
uv run python sig.py preset debug       # Set logging preset
uv run python sig.py logs --follow      # Follow logs
```

### **Direct API Commands (Alternative)**
```bash
# START
uv run python -m spigaui.main

# DEMO
curl -X POST http://localhost:8000/api/crawl/demo

# STATUS
curl http://localhost:8000/api/crawl/status/engine

# EMERGENCY STOP
curl -X POST http://localhost:8000/api/crawl/emergency/stop-all

# NUCLEAR OPTION
curl -X POST http://localhost:8000/api/crawl/emergency/kill-engine

# LOGGING
curl -X POST http://localhost:8000/api/crawl/logging/preset/debug
```

### **Log File Locations**
```bash
# Main log file
tail -f logs/spigaui.log

# Watch logs in real-time
tail -f logs/spigaui.log | grep -E "(error|performance|metrics)"
```

### **Useful Filters**
```bash
# Only errors
tail -f logs/spigaui.log | grep '"level": "error"'

# Only performance logs
tail -f logs/spigaui.log | grep '"category": "performance"'

# Only metrics
tail -f logs/spigaui.log | grep '"category": "metrics"'

# Specific job
tail -f logs/spigaui.log | grep '"job_id": "your-job-id"'
```

---

## 🛡️ **Safety Notes**

### **Process Management**
- **Use `sig.py kill --procs`** to properly kill both frontend and backend processes
- **Process killing requires psutil** - install with `uv add psutil`
- **Use `-9` flag only when normal termination fails** (force kill)
- **`sig.py start` manages both servers** - recommended for full development

### **API Safety**
- **Always use emergency stop** before nuclear reset
- **Monitor engine health** regularly during heavy testing
- **Nuclear reset destroys ALL data** - use only as last resort
- **API commands only affect jobs**, not server processes

### **Development**
- **Use appropriate logging levels** to avoid overwhelming output
- **Auto-reload is enabled** - server restarts on code changes
- **Logs are in JSON format** - use `jq` for pretty printing
- **Frontend runs on port 3000**, backend on port 8000

---

**SimWeaver provides a comprehensive, controllable environment for developing and testing SpigaUI. Use these commands to quickly control crawling behavior and logging output for your specific testing needs.**
