#!/usr/bin/env python3
"""
SimWeaver Signal Command Tool

Linux-like signal command interface for controlling SimWeaver crawler simulator.
Provides cross-platform control with argparse for clean command-line interface.

Usage:
    python sig.py start                    # Start both backend and frontend servers
    python sig.py start --backend          # Start backend server only
    python sig.py start --frontend         # Start frontend server only
    python sig.py restart                  # Restart both servers
    python sig.py restart --backend        # Restart backend server only
    python sig.py restart --frontend       # Restart frontend server only
    python sig.py kill                     # Emergency stop all jobs
    python sig.py kill -9                  # Nuclear reset (destroy all data)
    python sig.py status                   # Check engine status
    python sig.py demo                     # Create demo jobs
    python sig.py preset debug             # Set logging preset
    python sig.py logs --follow            # Follow logs in real-time
"""

import argparse
import json
import subprocess
import sys
import time
import psutil
import signal
import os
from pathlib import Path
from typing import Optional, Dict, Any, List

try:
    import requests
except ImportError:
    print("Error: requests library not found. Install with: pip install requests")
    sys.exit(1)

try:
    import psutil
except ImportError:
    print("Warning: psutil not found. Process killing features disabled.")
    print("Install with: pip install psutil")
    psutil = None

# Configuration
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api/crawl"
LOG_FILE = Path("logs/spigaui.log")

# ANSI color codes
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    BOLD = '\033[1m'
    NC = '\033[0m'  # No Color

def print_status(message: str):
    """Print status message in green."""
    print(f"{Colors.GREEN}[SimWeaver]{Colors.NC} {message}")

def print_warning(message: str):
    """Print warning message in yellow."""
    print(f"{Colors.YELLOW}[Warning]{Colors.NC} {message}")

def print_error(message: str):
    """Print error message in red."""
    print(f"{Colors.RED}[Error]{Colors.NC} {message}")

def print_info(message: str):
    """Print info message in blue."""
    print(f"{Colors.BLUE}[Info]{Colors.NC} {message}")

def print_signal(signal: str, message: str):
    """Print signal message in purple."""
    print(f"{Colors.PURPLE}[SIG{signal}]{Colors.NC} {message}")

def check_server() -> bool:
    """Check if SimWeaver server is running."""
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=2)
        return response.status_code == 200
    except requests.RequestException:
        return False

def find_spigaui_processes():
    """Find running SpigaUI backend processes."""
    if not psutil:
        print_error("psutil not available. Cannot find processes.")
        return []

    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('spigaui.main' in arg for arg in cmdline):
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def find_frontend_processes():
    """Find running frontend dev server processes."""
    if not psutil:
        print_error("psutil not available. Cannot find processes.")
        return []

    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('npm' in arg and 'dev' in ' '.join(cmdline) for arg in cmdline):
                processes.append(proc)
            # Also look for vite processes
            elif cmdline and any('vite' in arg for arg in cmdline):
                processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return processes

def kill_processes(processes, name: str, force: bool = False) -> int:
    """Kill a list of processes."""
    if not processes:
        print_info(f"No {name} processes found")
        return 0

    killed = 0
    for proc in processes:
        try:
            print_info(f"Killing {name} process {proc.pid}")
            if force:
                proc.kill()  # SIGKILL
            else:
                proc.terminate()  # SIGTERM
            killed += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print_warning(f"Could not kill process {proc.pid}: {e}")

    if not force and killed > 0:
        # Wait for graceful shutdown
        time.sleep(2)
        # Check if any are still running
        still_running = []
        for proc in processes:
            try:
                if proc.is_running():
                    still_running.append(proc)
            except psutil.NoSuchProcess:
                continue

        if still_running:
            print_warning(f"Some {name} processes still running, force killing...")
            kill_processes(still_running, name, force=True)

    return killed

def make_request(method: str, endpoint: str, data: Optional[Dict] = None) -> Optional[Dict[Any, Any]]:
    """Make HTTP request to SimWeaver API."""
    url = f"{API_URL}{endpoint}"
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        else:
            print_error(f"Unsupported HTTP method: {method}")
            return None
        
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        print_error(f"Request failed: {e}")
        return None

def start_backend():
    """Start backend server only."""
    print_signal("START", "Starting backend server...")
    try:
        subprocess.run([sys.executable, "-m", "spigaui.main"], check=True)
        print_status("SUCCESS: Backend server started")
    except subprocess.CalledProcessError as e:
        print_error(f"FAILED: Could not start backend server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_info("Backend server startup interrupted")
        sys.exit(0)

def start_frontend():
    """Start frontend server only."""
    print_signal("START", "Starting frontend server...")
    frontend_dir = Path(__file__).parent / "frontend"
    try:
        # Use shell=True to ensure npm is found in PATH
        subprocess.run("npm run dev", cwd=frontend_dir, shell=True, check=True)
        print_status("SUCCESS: Frontend server started")
    except subprocess.CalledProcessError as e:
        print_error(f"FAILED: Could not start frontend server: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print_info("Frontend server startup interrupted")
        sys.exit(0)

def start_both():
    """Start both backend and frontend servers."""
    print_signal("START", "Starting both servers...")
    root_dir = Path(__file__).parent
    frontend_dir = root_dir / "frontend"

    try:
        # Start backend
        print_info("Starting backend server...")
        backend = subprocess.Popen([
            sys.executable, "-m", "spigaui.main"
        ], cwd=root_dir)

        # Wait a moment
        time.sleep(2)

        # Start frontend
        print_info("Starting frontend server...")
        frontend = subprocess.Popen(
            "npm run dev",
            cwd=frontend_dir,
            shell=True
        )

        print_status("Servers started:")
        print_info("Backend:  http://127.0.0.1:8000")
        print_info("Frontend: http://localhost:3000")
        print_info("Press Ctrl+C to stop both servers")

        # Wait for interrupt
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print_info("Stopping servers...")
            backend.terminate()
            frontend.terminate()
            try:
                backend.wait(timeout=5)
                frontend.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend.kill()
                frontend.kill()
            print_status("All servers stopped")

    except Exception as e:
        print_error(f"Error starting servers: {e}")
        sys.exit(1)

def restart_servers(backend: bool = False, frontend: bool = False):
    """Restart backend, frontend, or both servers."""
    if not backend and not frontend:
        # Default to both if no specific option given
        backend = frontend = True

    print_signal("RESTART", "Restarting servers...")

    # Step 1: Kill existing processes
    if psutil:
        if backend:
            backend_procs = find_spigaui_processes()
            if backend_procs:
                print_info("Stopping backend processes...")
                kill_processes(backend_procs, "backend", force=False)

        if frontend:
            frontend_procs = find_frontend_processes()
            if frontend_procs:
                print_info("Stopping frontend processes...")
                kill_processes(frontend_procs, "frontend", force=False)
    else:
        print_warning("psutil not available - cannot kill existing processes")
        print_info("You may need to manually stop running servers")

    # Step 2: Wait a moment for cleanup
    time.sleep(2)

    # Step 3: Start requested servers
    if backend and frontend:
        start_both()
    elif backend:
        start_backend()
    elif frontend:
        start_frontend()

def kill_jobs(nuclear: bool = False, processes: bool = False):
    """Kill jobs or perform nuclear reset."""
    if processes:
        # Kill actual processes
        if not psutil:
            print_error("Cannot kill processes: psutil not available")
            print_info("Install with: pip install psutil")
            sys.exit(1)

        print_signal("KILL", "Killing SpigaUI processes...")

        backend_procs = find_spigaui_processes()
        frontend_procs = find_frontend_processes()

        if not backend_procs and not frontend_procs:
            print_status("SUCCESS: No SpigaUI processes found running")
            return

        total_killed = 0
        backend_killed = kill_processes(backend_procs, "backend", force=nuclear)
        frontend_killed = kill_processes(frontend_procs, "frontend", force=nuclear)
        total_killed = backend_killed + frontend_killed

        if total_killed > 0:
            signal_type = "KILL" if nuclear else "TERM"
            print_status(f"SUCCESS: Killed {total_killed} processes ({backend_killed} backend, {frontend_killed} frontend)")
        else:
            print_error("FAILED: Could not kill any processes")
            sys.exit(1)
        return

    # Original API-based job killing
    if not check_server():
        print_error("FAILED: Server is not running")
        sys.exit(1)

    if nuclear:
        print_signal("KILL", "SIGKILL (-9) - Nuclear reset (destroys ALL data)")
        confirm = input(f"{Colors.RED}Are you sure? This will destroy all job data. Type 'yes' to confirm: {Colors.NC}")
        if confirm.lower() != 'yes':
            print_info("Operation cancelled")
            return

        result = make_request("POST", "/emergency/kill-engine")
        if result:
            print_status("SUCCESS: Nuclear reset completed")
            print(json.dumps(result, indent=2))
        else:
            print_error("FAILED: Nuclear reset failed")
            sys.exit(1)
    else:
        print_signal("TERM", "SIGTERM - Emergency stopping all jobs (safe)")
        result = make_request("POST", "/emergency/stop-all")
        if result:
            print_status("SUCCESS: All jobs stopped safely")
            print(json.dumps(result, indent=2))
        else:
            print_error("FAILED: Could not stop jobs")
            sys.exit(1)

def show_status():
    """Show SimWeaver engine status."""
    if not check_server():
        print_error("Server is not running. Use 'python sig.py start' to start it.")
        return
    
    print_signal("STATUS", "Getting engine status...")
    result = make_request("GET", "/status/engine")
    if result:
        # Pretty print status
        healthy = result.get('engine_healthy', False)
        status_color = Colors.GREEN if healthy else Colors.RED
        print(f"\n{Colors.BOLD}Engine Status:{Colors.NC} {status_color}{'HEALTHY' if healthy else 'UNHEALTHY'}{Colors.NC}")
        
        print(f"{Colors.BOLD}Jobs:{Colors.NC}")
        print(f"  Total: {result.get('total_jobs', 0)}")
        print(f"  Active: {result.get('active_jobs', 0)}")
        print(f"  Active Timers: {result.get('active_timers', 0)}")
        
        if result.get('warnings'):
            print(f"{Colors.YELLOW}Warnings:{Colors.NC}")
            for warning in result['warnings']:
                print(f"  - {warning}")
        
        if result.get('job_status_breakdown'):
            print(f"{Colors.BOLD}Job Status Breakdown:{Colors.NC}")
            for status, count in result['job_status_breakdown'].items():
                print(f"  {status}: {count}")

def create_demo():
    """Create demo crawl jobs."""
    if not check_server():
        print_error("Server is not running. Use 'python sig.py start' to start it.")
        return
    
    print_signal("DEMO", "Creating demo crawl jobs...")
    result = make_request("POST", "/demo")
    if result:
        print_signal("DEMO", f"Created {len(result.get('job_ids', []))} demo jobs")
        print(json.dumps(result, indent=2))

def set_preset(preset_name: str):
    """Set logging preset."""
    if not check_server():
        print_error("Server is not running. Use 'python sig.py start' to start it.")
        return
    
    print_signal("PRESET", f"Setting logging preset to: {preset_name}")
    result = make_request("POST", f"/logging/preset/{preset_name}")
    if result:
        print_signal("PRESET", f"Logging preset '{preset_name}' activated")
        enabled = result.get('enabled_categories', [])
        print(f"Enabled categories: {', '.join(enabled)}")

def list_jobs():
    """List all jobs."""
    if not check_server():
        print_error("Server is not running. Use 'python sig.py start' to start it.")
        return
    
    print_signal("JOBS", "Listing all jobs...")
    result = make_request("GET", "")
    if result:
        jobs = result.get('jobs', [])
        if not jobs:
            print_info("No jobs found")
            return
        
        print(f"\n{Colors.BOLD}Jobs ({len(jobs)} total):{Colors.NC}")
        for job in jobs:
            status = job.get('status', 'unknown')
            status_color = {
                'running': Colors.GREEN,
                'completed': Colors.BLUE,
                'failed': Colors.RED,
                'queued': Colors.YELLOW
            }.get(status, Colors.WHITE)
            
            print(f"  {status_color}{status.upper()}{Colors.NC} {job.get('name', 'Unnamed')} ({job.get('job_id', 'no-id')[:8]}...)")
            if job.get('stats'):
                stats = job['stats']
                print(f"    Pages: {stats.get('pages_crawled', 0)}/{stats.get('total_pages', 0)} "
                      f"Failed: {stats.get('pages_failed', 0)} "
                      f"Rate: {stats.get('pages_per_second', 0):.1f}/s")

def follow_logs(filter_type: Optional[str] = None):
    """Follow logs in real-time."""
    if not LOG_FILE.exists():
        print_error(f"Log file not found: {LOG_FILE}")
        return
    
    filter_msg = f" (filtering: {filter_type})" if filter_type else ""
    print_signal("LOGS", f"Following logs{filter_msg} - Press Ctrl+C to stop")
    
    try:
        # Simple tail -f implementation
        with open(LOG_FILE, 'r') as f:
            # Go to end of file
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    if filter_type:
                        if filter_type in line:
                            print(line.rstrip())
                    else:
                        print(line.rstrip())
                else:
                    time.sleep(0.1)
    except KeyboardInterrupt:
        print_info("\nLog following stopped")

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="SimWeaver Signal Command Tool - Linux-like control interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python sig.py start                    # Start both backend and frontend servers
  python sig.py start --backend          # Start backend server only
  python sig.py start --frontend         # Start frontend server only
  python sig.py restart                  # Restart both servers
  python sig.py restart --backend        # Restart backend server only
  python sig.py restart --frontend       # Restart frontend server only
  python sig.py kill                     # Emergency stop all jobs (API)
  python sig.py kill --procs             # Kill server processes (SIGTERM)
  python sig.py kill --procs -9          # Force kill server processes (SIGKILL)
  python sig.py kill -9                  # Nuclear reset via API (destroys data)
  python sig.py status                   # Check engine status
  python sig.py demo                     # Create demo jobs
  python sig.py preset debug             # Set logging preset
  python sig.py logs --follow            # Follow logs in real-time
  python sig.py logs --filter error      # Follow error logs only
  python sig.py jobs                     # List all jobs

Available logging presets:
  minimal, debug, performance, error_testing, network, state_tracking, full_trace, production
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Start command
    start_parser = subparsers.add_parser('start', help='Start development servers')
    start_parser.add_argument('--backend', action='store_true', help='Start backend server only')
    start_parser.add_argument('--frontend', action='store_true', help='Start frontend server only')

    # Restart command
    restart_parser = subparsers.add_parser('restart', help='Restart development servers')
    restart_parser.add_argument('--backend', action='store_true', help='Restart backend server only')
    restart_parser.add_argument('--frontend', action='store_true', help='Restart frontend server only')
    
    # Kill command
    kill_parser = subparsers.add_parser('kill', help='Stop jobs or kill processes')
    kill_parser.add_argument('-9', '--nuclear', action='store_true',
                           help='Force kill with SIGKILL (use with --processes) or nuclear reset (API)')
    kill_parser.add_argument('--processes', '--procs', action='store_true',
                           help='Kill actual server processes instead of just stopping jobs')
    
    # Status command
    subparsers.add_parser('status', help='Show engine status')
    
    # Demo command
    subparsers.add_parser('demo', help='Create demo crawl jobs')
    
    # Preset command
    preset_parser = subparsers.add_parser('preset', help='Set logging preset')
    preset_parser.add_argument('name', help='Preset name (minimal, debug, performance, etc.)')
    
    # Jobs command
    subparsers.add_parser('jobs', help='List all jobs')
    
    # Logs command
    logs_parser = subparsers.add_parser('logs', help='View logs')
    logs_parser.add_argument('--follow', '-f', action='store_true', help='Follow logs in real-time')
    logs_parser.add_argument('--filter', help='Filter logs by keyword (error, metrics, etc.)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Execute commands
    if args.command == 'start':
        if args.backend and args.frontend:
            print_error("Cannot specify both --backend and --frontend. Use neither for both servers.")
            sys.exit(1)
        elif args.backend:
            start_backend()
        elif args.frontend:
            start_frontend()
        else:
            start_both()
    elif args.command == 'restart':
        if args.backend and args.frontend:
            print_error("Cannot specify both --backend and --frontend. Use neither for both servers.")
            sys.exit(1)
        else:
            restart_servers(backend=args.backend, frontend=args.frontend)
    elif args.command == 'kill':
        kill_jobs(nuclear=args.nuclear, processes=args.processes)
    elif args.command == 'status':
        show_status()
    elif args.command == 'demo':
        create_demo()
    elif args.command == 'preset':
        set_preset(args.name)
    elif args.command == 'jobs':
        list_jobs()
    elif args.command == 'logs':
        if args.follow:
            follow_logs(args.filter)
        else:
            # Just show recent logs
            if LOG_FILE.exists():
                with open(LOG_FILE, 'r') as f:
                    lines = f.readlines()
                    recent_lines = lines[-50:]  # Show last 50 lines
                    for line in recent_lines:
                        if args.filter:
                            if args.filter in line:
                                print(line.rstrip())
                        else:
                            print(line.rstrip())
            else:
                print_error(f"Log file not found: {LOG_FILE}")

if __name__ == "__main__":
    main()
