import React, { useState, useEffect, useRef } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { loggingApi, LogEntry } from '../lib/api'

const Logs: React.FC = () => {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')
  const [isAutoScroll, setIsAutoScroll] = useState(true)
  const [isStreaming, setIsStreaming] = useState(false)
  const logsEndRef = useRef<HTMLDivElement>(null)
  const queryClient = useQueryClient()

  // Get available logging presets
  const { data: presets, isLoading: presetsLoading } = useQuery({
    queryKey: ['logging-presets'],
    queryFn: loggingApi.getPresets,
  })

  // Mutation for setting logging preset
  const setPresetMutation = useMutation({
    mutationFn: loggingApi.setPreset,
    onSuccess: (data) => {
      alert(data.message)
      setLogs([]) // Clear logs when changing preset
    },
    onError: () => {
      alert('Failed to set logging preset')
    },
  })

  // Mutation for testing logging
  const testLoggingMutation = useMutation({
    mutationFn: loggingApi.testLogging,
    onSuccess: (data) => {
      alert(data.message)
    },
    onError: () => {
      alert('Failed to test logging')
    },
  })

  // Auto-scroll to bottom when new logs arrive (only if user hasn't scrolled up)
  useEffect(() => {
    if (isAutoScroll && logsEndRef.current) {
      // Only auto-scroll if the user is near the bottom (within 100px)
      const container = logsEndRef.current.parentElement
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        if (isNearBottom) {
          logsEndRef.current.scrollIntoView({ behavior: 'smooth' })
        }
      }
    }
  }, [logs, isAutoScroll])

  // Real log streaming using polling (in production, use SSE or WebSocket)
  useEffect(() => {
    if (!isStreaming) return

    const fetchLogs = async () => {
      try {
        const recentLogs = await loggingApi.getRecentLogs(20)
        setLogs(prev => {
          // Merge new logs with existing ones, avoiding duplicates
          const existingTimestamps = new Set(prev.map(log => log.timestamp))
          const newLogs = recentLogs.filter(log => !existingTimestamps.has(log.timestamp))
          return [...prev, ...newLogs].slice(-100) // Keep last 100 logs
        })
      } catch (error) {
        console.error('Error fetching logs:', error)
      }
    }

    // Initial fetch
    fetchLogs()

    // Poll for new logs every 2 seconds
    const interval = setInterval(fetchLogs, 2000)

    return () => clearInterval(interval)
  }, [isStreaming])

  const handlePresetChange = (presetName: string) => {
    setPresetMutation.mutate(presetName)
  }

  const handleTestLogging = () => {
    testLoggingMutation.mutate()
  }

  const handleStartStreaming = async () => {
    setIsStreaming(true)
    // Load initial logs
    try {
      const initialLogs = await loggingApi.getRecentLogs(50)
      setLogs(initialLogs)
    } catch (error) {
      console.error('Error loading initial logs:', error)
      // Add fallback log entry
      const initialLog: LogEntry = {
        timestamp: new Date().toISOString(),
        level: 'info',
        category: 'state',
        event: 'Log streaming started',
        message: 'Started real-time log monitoring'
      }
      setLogs([initialLog])
    }
  }

  const handleStopStreaming = () => {
    setIsStreaming(false)
  }

  const handleClearLogs = () => {
    setLogs([])
  }

  // Filter logs based on selected category and level
  const filteredLogs = logs.filter(log => {
    const categoryMatch = selectedCategory === 'all' || log.category === selectedCategory
    const levelMatch = selectedLevel === 'all' || log.level === selectedLevel
    return categoryMatch && levelMatch
  })

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-400'
      case 'warning': return 'text-yellow-400'
      case 'info': return 'text-blue-400'
      case 'debug': return 'text-gray-400'
      default: return 'text-white'
    }
  }

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case 'performance': return 'bg-blue-600'
      case 'error': return 'bg-red-600'
      case 'debug': return 'bg-gray-600'
      case 'security': return 'bg-purple-600'
      case 'network': return 'bg-green-600'
      case 'state': return 'bg-yellow-600'
      case 'metrics': return 'bg-indigo-600'
      default: return 'bg-gray-500'
    }
  }

  if (presetsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Logs</h1>
        <p className="text-gray-400">Real-time SimWeaver logging and monitoring</p>
      </div>

      {/* Controls */}
      <div className="sticky top-0 z-10 bg-gray-800 rounded-lg p-6 mb-6 border border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Logging Presets */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Logging Preset
            </label>
            <select
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              onChange={(e) => handlePresetChange(e.target.value)}
              disabled={setPresetMutation.isPending}
            >
              <option value="">Select preset...</option>
              {presets && Object.entries(presets.presets).map(([key, description]) => (
                <option key={key} value={key}>
                  {key} - {description}
                </option>
              ))}
            </select>
          </div>

          {/* Category Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Category Filter
            </label>
            <select
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="all">All Categories</option>
              <option value="performance">Performance</option>
              <option value="error">Error</option>
              <option value="debug">Debug</option>
              <option value="security">Security</option>
              <option value="network">Network</option>
              <option value="state">State</option>
              <option value="metrics">Metrics</option>
            </select>
          </div>

          {/* Level Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Level Filter
            </label>
            <select
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              value={selectedLevel}
              onChange={(e) => setSelectedLevel(e.target.value)}
            >
              <option value="all">All Levels</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
          </div>

          {/* Auto-scroll Toggle */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Options
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isAutoScroll}
                onChange={(e) => setIsAutoScroll(e.target.checked)}
                className="mr-2"
              />
              <span className="text-white text-sm">Auto-scroll</span>
            </label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={isStreaming ? handleStopStreaming : handleStartStreaming}
            className={`px-4 py-2 rounded-lg font-medium ${
              isStreaming
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-green-600 hover:bg-green-700 text-white'
            }`}
          >
            {isStreaming ? 'Stop Streaming' : 'Start Streaming'}
          </button>
          
          <button
            onClick={handleTestLogging}
            disabled={testLoggingMutation.isPending}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium disabled:opacity-50"
          >
            Test Logging
          </button>
          
          <button
            onClick={handleClearLogs}
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium"
          >
            Clear Logs
          </button>
        </div>
      </div>

      {/* Log Display */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
          <h2 className="text-lg font-medium text-white">
            Live Logs ({filteredLogs.length})
          </h2>
          <div className="flex items-center space-x-2">
            {isStreaming && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                <span className="text-green-400 text-sm">Streaming</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="h-96 overflow-y-auto bg-gray-900 font-mono text-sm">
          {filteredLogs.length > 0 ? (
            <div className="p-4 space-y-2">
              {filteredLogs.map((log, index) => (
                <div key={index} className="flex items-start space-x-3 py-1">
                  <span className="text-gray-500 text-xs whitespace-nowrap">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                  <span className={`text-xs font-medium ${getLevelColor(log.level)} uppercase`}>
                    {log.level}
                  </span>
                  {log.category && (
                    <span className={`text-xs px-2 py-1 rounded ${getCategoryColor(log.category)} text-white`}>
                      {log.category}
                    </span>
                  )}
                  <span className="text-white flex-1">
                    {log.event} {log.message && `- ${log.message}`}
                  </span>
                </div>
              ))}
              <div ref={logsEndRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <div className="text-center">
                <p className="mb-2">No logs to display</p>
                <p className="text-sm">
                  {isStreaming ? 'Waiting for log entries...' : 'Click "Start Streaming" to begin monitoring logs'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Bottom Controls */}
      {isStreaming && (
        <div className="fixed bottom-4 right-4 z-20">
          <button
            onClick={handleStopStreaming}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium shadow-lg flex items-center space-x-2"
          >
            <span>⏹️</span>
            <span>Stop Streaming</span>
          </button>
        </div>
      )}
    </div>
  )
}

export default Logs
